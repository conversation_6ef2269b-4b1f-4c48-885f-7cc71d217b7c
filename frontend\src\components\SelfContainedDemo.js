import React, { useState, useEffect } from 'react';
import { SelfContainedTemplateRenderer } from './SelfContainedTemplateEngine';

/**
 * DEMONSTRATION: Self-Contained Templates
 * 
 * This shows how templates can be completely autonomous:
 * - No external React components needed
 * - No registry mapping required
 * - Templates contain their own rendering logic
 * - Just load JSON and render directly
 */

const SelfContainedDemo = () => {
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [config, setConfig] = useState({});
  const [loading, setLoading] = useState(true);

  // Load self-contained template examples
  useEffect(() => {
    loadSelfContainedTemplates();
  }, []);

  const loadSelfContainedTemplates = async () => {
    try {
      // In a real implementation, this would come from the backend
      // For demo purposes, we'll use the example templates
      const exampleTemplates = [
        {
          "id": "demo_flip_card",
          "name": "Self-Contained 3D Flip Card",
          "type": "three_d_flip_card_animation",
          "category": "ui",
          "media_type": "animation",
          "description": "A completely self-contained 3D flip card",
          
          "default_config": {
            "front_text": "HELLO",
            "back_text": "WORLD",
            "width": "300px",
            "height": "200px",
            "perspective": "1000px",
            "flip_axis": "Y",
            "flip_degrees": 180,
            "duration": 0.8,
            "easing": "ease-in-out",
            "flip_delay": 500,
            "front_bg": "#ffffff",
            "back_bg": "#111827",
            "front_text_color": "#000000",
            "back_text_color": "#ffffff",
            "border_radius": "12px",
            "font_size": "24px",
            "font_weight": "bold",
            "shadow": true
          },
          
          "renderFunction": `
            const [isFlipped, setIsFlipped] = useState(false);
            const [isAnimating, setIsAnimating] = useState(false);
            
            useEffect(() => {
              if (!isPlaying) {
                setIsFlipped(false);
                setIsAnimating(false);
                return;
              }
              
              setIsAnimating(true);
              const flipDelay = config.flip_delay || 500;
              
              const timer = createTimer(() => {
                setIsFlipped(true);
                
                const duration = (config.duration || 0.8) * 1000;
                const completeTimer = createTimer(() => {
                  setIsAnimating(false);
                  onComplete?.();
                }, duration);
                
                return () => clearTimer(completeTimer);
              }, flipDelay);
              
              return () => clearTimer(timer);
            }, [isPlaying]);
            
            return React.createElement('div', {
              style: {
                width: config.width || '300px',
                height: config.height || '200px',
                perspective: config.perspective || '1000px',
                margin: '20px auto'
              }
            }, [
              React.createElement('div', {
                key: 'card-inner',
                style: {
                  position: 'relative',
                  width: '100%',
                  height: '100%',
                  textAlign: 'center',
                  transformStyle: 'preserve-3d',
                  transform: isFlipped ? \`rotate\${config.flip_axis || 'Y'}(\${config.flip_degrees || 180}deg)\` : 'rotateY(0deg)',
                  transition: \`transform \${config.duration || 0.8}s \${config.easing || 'ease-in-out'}\`
                }
              }, [
                // Front face
                React.createElement('div', {
                  key: 'front',
                  style: {
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    backfaceVisibility: 'hidden',
                    background: config.front_bg || '#ffffff',
                    color: config.front_text_color || '#000000',
                    borderRadius: config.border_radius || '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: config.font_size || '24px',
                    fontWeight: config.font_weight || 'bold',
                    boxShadow: config.shadow ? '0 8px 25px rgba(0,0,0,0.3)' : 'none',
                    fontFamily: 'Arial, sans-serif'
                  }
                }, config.front_text || 'FRONT'),
                
                // Back face
                React.createElement('div', {
                  key: 'back',
                  style: {
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    backfaceVisibility: 'hidden',
                    background: config.back_bg || '#111827',
                    color: config.back_text_color || '#ffffff',
                    borderRadius: config.border_radius || '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: config.font_size || '24px',
                    fontWeight: config.font_weight || 'bold',
                    boxShadow: config.shadow ? '0 8px 25px rgba(0,0,0,0.3)' : 'none',
                    transform: 'rotateY(180deg)',
                    fontFamily: 'Arial, sans-serif'
                  }
                }, config.back_text || 'BACK')
              ])
            ]);
          `
        },
        
        {
          "id": "demo_money_counter",
          "name": "Self-Contained Money Counter",
          "type": "money_counter_animation",
          "category": "counters",
          "media_type": "animation",
          "description": "A completely self-contained money counter",
          
          "default_config": {
            "start_value": 0,
            "end_value": 1000,
            "duration": 2000,
            "increment": 1,
            "prefix": "$",
            "suffix": "",
            "font_size": "48px",
            "font_weight": "bold",
            "color": "#22c55e",
            "background": "transparent",
            "padding": "20px",
            "border_radius": "8px",
            "font_family": "Arial, sans-serif",
            "format_as_currency": true
          },
          
          "renderFunction": `
            const [currentValue, setCurrentValue] = useState(config.start_value || 0);
            const [isAnimating, setIsAnimating] = useState(false);
            
            useEffect(() => {
              if (!isPlaying) {
                setCurrentValue(config.start_value || 0);
                setIsAnimating(false);
                return;
              }
              
              setIsAnimating(true);
              const startValue = config.start_value || 0;
              const endValue = config.end_value || 100;
              const duration = config.duration || 2000;
              const increment = config.increment || 1;
              
              const steps = Math.abs(endValue - startValue) / increment;
              const stepDuration = duration / steps;
              
              let current = startValue;
              const timer = createInterval(() => {
                if (startValue < endValue ? current >= endValue : current <= endValue) {
                  setCurrentValue(endValue);
                  clearInterval(timer);
                  setIsAnimating(false);
                  onComplete?.();
                } else {
                  current += startValue < endValue ? increment : -increment;
                  setCurrentValue(current);
                }
              }, stepDuration);
              
              return () => clearInterval(timer);
            }, [isPlaying]);
            
            const formatValue = (value) => {
              if (config.format_as_currency) {
                return new Intl.NumberFormat('en-US', { 
                  style: 'currency', 
                  currency: 'USD',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0
                }).format(value);
              }
              return value.toLocaleString();
            };
            
            return React.createElement('div', {
              style: {
                fontSize: config.font_size || '48px',
                fontWeight: config.font_weight || 'bold',
                color: config.color || '#22c55e',
                background: config.background || 'transparent',
                padding: config.padding || '20px',
                borderRadius: config.border_radius || '8px',
                textAlign: 'center',
                fontFamily: config.font_family || 'Arial, sans-serif',
                transition: 'all 0.3s ease',
                userSelect: 'none'
              }
            }, [
              config.prefix && !config.format_as_currency && React.createElement('span', { key: 'prefix' }, config.prefix),
              React.createElement('span', { 
                key: 'value',
                style: {
                  display: 'inline-block',
                  minWidth: '1ch'
                }
              }, formatValue(currentValue)),
              config.suffix && React.createElement('span', { key: 'suffix' }, config.suffix)
            ]);
          `
        }
      ];
      
      setTemplates(exampleTemplates);
      setSelectedTemplate(exampleTemplates[0]);
      setConfig(exampleTemplates[0].default_config);
      setLoading(false);
    } catch (error) {
      console.error('Failed to load templates:', error);
      setLoading(false);
    }
  };

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
    setConfig(template.default_config);
    setIsPlaying(false);
  };

  const handlePlay = () => {
    setIsPlaying(true);
  };

  const handleStop = () => {
    setIsPlaying(false);
  };

  const handleConfigChange = (key, value) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const handleComplete = () => {
    console.log('Animation completed!');
    setIsPlaying(false);
  };

  if (loading) {
    return (
      <div style={{ padding: '40px', textAlign: 'center' }}>
        <h2>Loading Self-Contained Templates...</h2>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ marginBottom: '30px', textAlign: 'center' }}>
        <h1 style={{ color: '#333', marginBottom: '10px' }}>🚀 Self-Contained Template Engine</h1>
        <p style={{ color: '#666', fontSize: '18px', maxWidth: '600px', margin: '0 auto' }}>
          <strong>No external React components needed!</strong> Templates contain their own rendering logic and are completely autonomous.
        </p>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '300px 1fr 300px', gap: '20px', alignItems: 'start' }}>
        {/* Template Selector */}
        <div style={{ background: '#f8f9fa', padding: '20px', borderRadius: '12px' }}>
          <h3 style={{ margin: '0 0 15px 0', color: '#333' }}>Available Templates</h3>
          {templates.map(template => (
            <div
              key={template.id}
              onClick={() => handleTemplateSelect(template)}
              style={{
                padding: '12px',
                margin: '8px 0',
                background: selectedTemplate?.id === template.id ? '#007bff' : '#ffffff',
                color: selectedTemplate?.id === template.id ? '#ffffff' : '#333',
                borderRadius: '8px',
                cursor: 'pointer',
                border: '1px solid #ddd',
                transition: 'all 0.2s ease'
              }}
            >
              <div style={{ fontWeight: 'bold', fontSize: '14px' }}>{template.name}</div>
              <div style={{ fontSize: '12px', opacity: 0.8, marginTop: '4px' }}>{template.description}</div>
            </div>
          ))}
          
          <div style={{ marginTop: '20px', padding: '15px', background: '#e8f5e8', borderRadius: '8px' }}>
            <h4 style={{ margin: '0 0 10px 0', color: '#2d5a2d' }}>✅ Benefits</h4>
            <ul style={{ margin: 0, paddingLeft: '20px', fontSize: '13px', color: '#2d5a2d' }}>
              <li>No React component files needed</li>
              <li>No registry mapping required</li>
              <li>Templates are self-executing</li>
              <li>Infinitely scalable</li>
              <li>Zero external dependencies</li>
            </ul>
          </div>
        </div>

        {/* Preview Area */}
        <div style={{ background: '#ffffff', padding: '30px', borderRadius: '12px', border: '1px solid #ddd', textAlign: 'center' }}>
          <div style={{ marginBottom: '20px' }}>
            <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>{selectedTemplate?.name}</h3>
            <div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
              <button
                onClick={handlePlay}
                disabled={isPlaying}
                style={{
                  padding: '10px 20px',
                  background: isPlaying ? '#ccc' : '#28a745',
                  color: '#ffffff',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: isPlaying ? 'not-allowed' : 'pointer',
                  fontWeight: 'bold'
                }}
              >
                {isPlaying ? '▶️ Playing...' : '▶️ Play'}
              </button>
              <button
                onClick={handleStop}
                style={{
                  padding: '10px 20px',
                  background: '#dc3545',
                  color: '#ffffff',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontWeight: 'bold'
                }}
              >
                ⏹️ Stop
              </button>
            </div>
          </div>

          {/* Template Renderer */}
          <div style={{ 
            minHeight: '300px', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            background: '#f8f9fa',
            borderRadius: '8px',
            border: '2px dashed #ddd'
          }}>
            {selectedTemplate && (
              <SelfContainedTemplateRenderer
                template={selectedTemplate}
                config={config}
                isPlaying={isPlaying}
                onComplete={handleComplete}
              />
            )}
          </div>
        </div>

        {/* Configuration Panel */}
        <div style={{ background: '#f8f9fa', padding: '20px', borderRadius: '12px' }}>
          <h3 style={{ margin: '0 0 15px 0', color: '#333' }}>Configuration</h3>
          
          {selectedTemplate && Object.entries(selectedTemplate.default_config).slice(0, 8).map(([key, value]) => (
            <div key={key} style={{ marginBottom: '12px' }}>
              <label style={{ display: 'block', fontSize: '12px', fontWeight: 'bold', marginBottom: '4px', color: '#555' }}>
                {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </label>
              
              {typeof value === 'boolean' ? (
                <input
                  type="checkbox"
                  checked={config[key] || false}
                  onChange={(e) => handleConfigChange(key, e.target.checked)}
                  style={{ transform: 'scale(1.2)' }}
                />
              ) : typeof value === 'number' ? (
                <input
                  type="number"
                  value={config[key] || value}
                  onChange={(e) => handleConfigChange(key, parseFloat(e.target.value) || 0)}
                  style={{
                    width: '100%',
                    padding: '6px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '13px'
                  }}
                />
              ) : key.includes('color') ? (
                <input
                  type="color"
                  value={config[key] || value}
                  onChange={(e) => handleConfigChange(key, e.target.value)}
                  style={{
                    width: '100%',
                    height: '30px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                />
              ) : (
                <input
                  type="text"
                  value={config[key] || value}
                  onChange={(e) => handleConfigChange(key, e.target.value)}
                  style={{
                    width: '100%',
                    padding: '6px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '13px'
                  }}
                />
              )}
            </div>
          ))}
          
          <div style={{ marginTop: '20px', padding: '15px', background: '#fff3cd', borderRadius: '8px' }}>
            <h4 style={{ margin: '0 0 10px 0', color: '#856404' }}>💡 How It Works</h4>
            <p style={{ margin: 0, fontSize: '13px', color: '#856404' }}>
              Each template contains its own <code>renderFunction</code> with complete React logic. 
              No external components needed - just pure JSON templates!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SelfContainedDemo;