# Animation Template Creation Guide

This guide explains how to create new animation templates for the MotionStock system.

## Overview

The MotionStock system loads animation templates from JSON files in the frontend. Each template contains its own complete React component code in the renderFunction field - no backend required.

## Template Format

Use this **SINGLE WORKING FORMAT** for all templates:

### Template File Structure

Create a JSON file in the `frontend/public/templates/` directory:

```json
{
  "id": "unique_animation_id",
  "name": "Display Name",
  "type": "animation_type",
  "category": "category_name",
  "description": "Brief description of the animation",
  "default_config": {
    "parameter_name": "default_value",
    "color": "#ff0000",
    "size": 50,
    "enabled": true
  },
  "editable_params": [
    {
      "name": "parameter_name",
      "type": "text|range|color|boolean|select",
      "label": "User-friendly Label",
      "min": 0,
      "max": 100,
      "step": 1,
      "options": ["option1", "option2"]
    }
  ],
  "renderFunction": "function(config, context) { /* Complete React component code */ }"
}
```

### Parameter Types

- **text**: Text input field
- **range**: Numeric slider with min/max/step
- **color**: Color picker
- **boolean**: Checkbox (true/false)
- **select**: Dropdown with predefined options

### Complete Example Template

```json
{
  "id": "money_counter",
  "name": "Money Counter",
  "type": "counter",
  "category": "business",
  "description": "Animated number counter with currency formatting",
  "default_config": {
    "start_value": 0,
    "end_value": 1000000,
    "duration": 3000,
    "currency": "$",
    "decimal_places": 0,
    "color": "#00ff88",
    "font_size": "48px",
    "font_weight": "bold",
    "background": "#000000",
    "easing": "ease-out",
    "glow_effect": true
  },
  "editable_params": [
    {
      "name": "start_value",
      "type": "number",
      "label": "Start Value",
      "default": 0,
      "min": 0,
      "max": 999999999
    },
    {
      "name": "end_value",
      "type": "number",
      "label": "End Value",
      "default": 1000000,
      "min": 0,
      "max": 999999999
    },
    {
      "name": "duration",
      "type": "number",
      "label": "Duration (ms)",
      "default": 3000,
      "min": 500,
      "max": 10000
    },
    {
      "name": "currency",
      "type": "string",
      "label": "Currency Symbol",
      "default": "$"
    },
    {
      "name": "decimal_places",
      "type": "number",
      "label": "Decimal Places",
      "default": 0,
      "min": 0,
      "max": 4
    },
    {
      "name": "color",
      "type": "color",
      "label": "Text Color",
      "default": "#00ff88"
    },
    {
      "name": "font_size",
      "type": "string",
      "label": "Font Size",
      "default": "48px"
    },
    {
      "name": "background",
      "type": "color",
      "label": "Background Color",
      "default": "#000000"
    },
    {
      "name": "easing",
      "type": "select",
      "label": "Animation Easing",
      "default": "ease-out",
      "options": ["linear", "ease-in", "ease-out", "ease-in-out"]
    },
    {
      "name": "glow_effect",
      "type": "boolean",
      "label": "Glow Effect",
      "default": true
    }
  ],
  "renderFunction": "function(config, context) {\n  const { useState, useEffect, useRef } = context.React;\n  const [currentValue, setCurrentValue] = useState(config.start_value);\n  const [isAnimating, setIsAnimating] = useState(false);\n  const animationRef = useRef(null);\n  \n  useEffect(() => {\n    if (context.isPlaying && !isAnimating) {\n      setIsAnimating(true);\n      const startTime = Date.now();\n      const startValue = config.start_value;\n      const endValue = config.end_value;\n      const duration = config.duration;\n      \n      const animate = () => {\n        const elapsed = Date.now() - startTime;\n        const progress = Math.min(elapsed / duration, 1);\n        \n        let easedProgress;\n        switch (config.easing) {\n          case 'ease-in':\n            easedProgress = progress * progress;\n            break;\n          case 'ease-out':\n            easedProgress = 1 - Math.pow(1 - progress, 2);\n            break;\n          case 'ease-in-out':\n            easedProgress = progress < 0.5 ? 2 * progress * progress : 1 - Math.pow(-2 * progress + 2, 2) / 2;\n            break;\n          default:\n            easedProgress = progress;\n        }\n        \n        const value = startValue + (endValue - startValue) * easedProgress;\n        setCurrentValue(value);\n        \n        if (progress < 1) {\n          animationRef.current = requestAnimationFrame(animate);\n        } else {\n          setIsAnimating(false);\n          if (context.onComplete) {\n            context.onComplete();\n          }\n        }\n      };\n      \n      animationRef.current = requestAnimationFrame(animate);\n    }\n    \n    return () => {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [context.isPlaying, config, isAnimating]);\n  \n  const formatValue = (value) => {\n    return config.currency + value.toFixed(config.decimal_places);\n  };\n  \n  const glowStyle = config.glow_effect ? {\n    textShadow: `0 0 10px ${config.color}, 0 0 20px ${config.color}, 0 0 30px ${config.color}`\n  } : {};\n  \n  return context.React.createElement('div', {\n    style: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      height: '100vh',\n      background: config.background,\n      fontFamily: 'Arial, sans-serif'\n    }\n  }, context.React.createElement('div', {\n    style: {\n      fontSize: config.font_size,\n      fontWeight: config.font_weight,\n      color: config.color,\n      ...glowStyle\n    }\n  }, formatValue(currentValue)));\n}"
}
```

## Required Fields

### Basic Template Information
- **id**: Unique identifier (lowercase, underscores allowed)
- **name**: Display name shown in UI
- **type**: Animation type identifier
- **category**: Category for organization (motion, ui, text, etc.)
- **media_type**: Always "animation" for animations
- **description**: Brief description of the animation
- **thumbnail**: Path to preview image

### Configuration
- **default_config**: Object with parameter names and their default values
- **editable_params**: Array of parameter definitions for the UI

## Parameter Configuration

Each parameter in `editable_params` must have:
- **name**: Must match a key in `default_config`
- **type**: One of: text, range, color, boolean, select
- **label**: User-friendly display name

### Optional Parameter Properties
- **min/max/step**: For range type parameters
- **options**: Array of choices for select type
- **placeholder**: Hint text for text inputs

## File Organization

```
backend/
├── data/
│   └── templates/
│       ├── money-counter.json
│       ├── fade-text.json
│       └── particle-burst.json
└── template_manager.py
```

## How It Works

1. **Auto-Discovery**: System scans `backend/data/templates/` for JSON files
2. **Validation**: Templates are validated for required fields
3. **Processing**: Templates are processed and added to the system
4. **UI Generation**: Parameter controls are automatically generated

## Best Practices

### Template Creation
- Use descriptive, unique IDs (lowercase with underscores)
- Provide sensible default values that create a good preview
- Write clear, helpful labels for all parameters
- Test parameter ranges to ensure they work well
- Keep descriptions concise but informative

### Parameter Design
- Use appropriate parameter types for the data
- Set reasonable min/max values for ranges
- Provide good default values that showcase the animation
- Group related parameters logically

### File Naming
- Use kebab-case for filenames (money-counter.json)
- Make filenames descriptive of the animation purpose
- Keep filenames short but clear

## Quick Start

1. Create a JSON file in `backend/data/templates/`
2. Use the format shown in the example above
3. Restart the backend server
4. Your template will be automatically discovered and available in the UI

## Common Issues

### Template Not Showing
- Check JSON syntax is valid
- Verify all required fields are present
- Ensure file is in `backend/data/templates/` directory
- Restart the backend server

### Parameters Not Working
- Ensure parameter names in `editable_params` match keys in `default_config`
- Check parameter types are correct (text, range, color, boolean, select)
- Verify min/max/step values for range parameters
- Make sure select parameters have an options array

## Summary

This is the **ONLY** format that works. Don't use any other format you might see in examples or documentation elsewhere. The system will automatically handle everything else once you create a properly formatted JSON template file.