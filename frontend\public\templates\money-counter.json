{"id": "money-counter", "name": "Money Counter", "type": "animation", "category": "counters", "description": "Animated money counter with customizable currency and formatting", "preview_url": "https://example.com/money-counter-preview.gif", "default_config": {"start_value": 0, "end_value": 1000, "duration": 3000, "currency": "$", "decimal_places": 2, "color": "#00ff00", "font_size": "48px", "font_weight": "bold", "background": "transparent", "easing": "ease-out", "glow_effect": true}, "editable_params": [{"name": "start_value", "type": "number", "label": "Start Value", "default": 0, "min": 0, "max": 1000000}, {"name": "end_value", "type": "number", "label": "End Value", "default": 1000, "min": 1, "max": 1000000}, {"name": "duration", "type": "number", "label": "Duration (ms)", "default": 3000, "min": 500, "max": 10000}, {"name": "currency", "type": "string", "label": "Currency Symbol", "default": "$"}, {"name": "decimal_places", "type": "number", "label": "Decimal Places", "default": 2, "min": 0, "max": 4}, {"name": "color", "type": "color", "label": "Text Color", "default": "#00ff00"}, {"name": "font_size", "type": "string", "label": "Font Size", "default": "48px"}, {"name": "font_weight", "type": "select", "label": "Font Weight", "default": "bold", "options": ["normal", "bold", "lighter", "bolder", "100", "200", "300", "400", "500", "600", "700", "800", "900"]}, {"name": "background", "type": "color", "label": "Background Color", "default": "transparent"}, {"name": "easing", "type": "select", "label": "Easing", "default": "ease-out", "options": ["linear", "ease", "ease-in", "ease-out", "ease-in-out"]}, {"name": "glow_effect", "type": "boolean", "label": "Glow Effect", "default": true}], "renderFunction": "function(config, context) {\n  const { useState, useEffect, useRef } = context.React;\n  const { createTimer } = context.utils;\n  \n  const [currentValue, setCurrentValue] = useState(config.start_value);\n  const [isAnimating, setIsAnimating] = useState(false);\n  const animationRef = useRef(null);\n  \n  useEffect(() => {\n    if (context.isPlaying && !isAnimating) {\n      setIsAnimating(true);\n      const startTime = Date.now();\n      const startValue = config.start_value;\n      const endValue = config.end_value;\n      const duration = config.duration;\n      \n      const animate = () => {\n        const elapsed = Date.now() - startTime;\n        const progress = Math.min(elapsed / duration, 1);\n        \n        // Easing function\n        let easedProgress;\n        switch (config.easing) {\n          case 'ease-in':\n            easedProgress = progress * progress;\n            break;\n          case 'ease-out':\n            easedProgress = 1 - Math.pow(1 - progress, 2);\n            break;\n          case 'ease-in-out':\n            easedProgress = progress < 0.5 ? 2 * progress * progress : 1 - Math.pow(-2 * progress + 2, 2) / 2;\n            break;\n          default:\n            easedProgress = progress;\n        }\n        \n        const value = startValue + (endValue - startValue) * easedProgress;\n        setCurrentValue(value);\n        \n        if (progress < 1) {\n          animationRef.current = requestAnimationFrame(animate);\n        } else {\n          setIsAnimating(false);\n          if (context.onComplete) {\n            context.onComplete();\n          }\n        }\n      };\n      \n      animationRef.current = requestAnimationFrame(animate);\n    }\n    \n    return () => {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [context.isPlaying, config, isAnimating]);\n  \n  const formatValue = (value) => {\n    return config.currency + value.toFixed(config.decimal_places);\n  };\n  \n  const glowStyle = config.glow_effect ? {\n    textShadow: `0 0 10px ${config.color}, 0 0 20px ${config.color}, 0 0 30px ${config.color}`\n  } : {};\n  \n  return context.React.createElement('div', {\n    style: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      height: '100vh',\n      background: config.background,\n      fontFamily: 'Arial, sans-serif'\n    }\n  }, context.React.createElement('div', {\n    style: {\n      fontSize: config.font_size,\n      fontWeight: config.font_weight,\n      color: config.color,\n      ...glowStyle\n    }\n  }, formatValue(currentValue)));\n}"}