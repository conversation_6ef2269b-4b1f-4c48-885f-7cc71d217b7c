import React, { useState, useEffect, useRef } from 'react';

/**
 * Universal Template Renderer
 * Fetches JSON templates and renders them directly without App.js dependencies
 * Supports mediabunny for advanced media processing
 */
class UniversalTemplateRenderer {
  constructor() {
    this.templateCache = new Map();
    this.mediabunnyLoaded = false;
    this.renderingContext = this.createRenderingContext();
  }

  // Create enhanced rendering context with mediabunny support
  createRenderingContext() {
    return {
      React,
      useState,
      useEffect,
      useRef,
      
      // Animation utilities
      createAnimation: (element, keyframes, options) => {
        if (element && element.animate) {
          return element.animate(keyframes, options);
        }
        return null;
      },
      
      // CSS utilities
      createCSS: (styles) => {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
        return styleSheet;
      },
      
      // Timer utilities
      createTimer: (callback, delay) => setTimeout(callback, delay),
      clearTimer: (timer) => clearTimeout(timer),
      createInterval: (callback, interval) => setInterval(callback, interval),
      clearInterval: (interval) => clearInterval(interval),
      
      // Canvas utilities
      createCanvas: (width = 800, height = 600) => {
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        return canvas;
      },
      
      // Math utilities
      Math,
      
      // Common animation easing functions
      easings: {
        linear: 'linear',
        easeIn: 'ease-in',
        easeOut: 'ease-out',
        easeInOut: 'ease-in-out',
        bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
      },
      
      // Mediabunny integration (loaded dynamically)
      mediabunny: null,
      
      // Media processing utilities
      processVideo: async (file, options = {}) => {
        if (!this.mediabunnyLoaded) {
          await this.loadMediabunny();
        }
        return this.processMediaFile(file, 'video', options);
      },
      
      processAudio: async (file, options = {}) => {
        if (!this.mediabunnyLoaded) {
          await this.loadMediabunny();
        }
        return this.processMediaFile(file, 'audio', options);
      },
      
      // File utilities
      loadFile: async (url) => {
        const response = await fetch(url);
        return response.blob();
      }
    };
  }

  // Load mediabunny dynamically
  async loadMediabunny() {
    try {
      const mediabunny = await import('mediabunny');
      this.renderingContext.mediabunny = mediabunny;
      this.mediabunnyLoaded = true;
      console.log('Mediabunny loaded successfully');
    } catch (error) {
      console.warn('Mediabunny not available, falling back to basic media handling:', error);
      // Provide fallback media utilities
      this.renderingContext.mediabunny = {
        Input: null,
        Output: null,
        Conversion: null
      };
    }
  }

  // Process media files using mediabunny
  async processMediaFile(file, type, options) {
    if (!this.renderingContext.mediabunny || !this.renderingContext.mediabunny.Input) {
      console.warn('Mediabunny not available, returning original file');
      return file;
    }

    try {
      const { Input, Output, Conversion, BlobSource, BufferTarget, ALL_FORMATS } = this.renderingContext.mediabunny;
      
      const input = new Input({
        source: new BlobSource(file),
        formats: ALL_FORMATS,
      });

      // Get file metadata
      const duration = await input.computeDuration();
      const metadata = {
        duration,
        type
      };

      if (type === 'video') {
        const videoTrack = await input.getPrimaryVideoTrack();
        metadata.width = videoTrack.displayWidth;
        metadata.height = videoTrack.displayHeight;
        metadata.rotation = videoTrack.rotation;
      } else if (type === 'audio') {
        const audioTrack = await input.getPrimaryAudioTrack();
        metadata.sampleRate = audioTrack.sampleRate;
        metadata.channels = audioTrack.numberOfChannels;
      }

      // Apply processing options if specified
      if (options.convert || options.resize || options.trim) {
        const output = new Output({
          format: options.outputFormat || input.format,
          target: new BufferTarget(),
        });

        const conversion = await Conversion.init({ input, output });
        
        if (options.resize && type === 'video') {
          conversion.resize(options.resize.width, options.resize.height);
        }
        
        if (options.trim) {
          conversion.trim(options.trim.start, options.trim.end);
        }
        
        await conversion.execute();
        const processedBuffer = output.target.buffer;
        const processedBlob = new Blob([processedBuffer]);
        
        return {
          file: processedBlob,
          metadata,
          processed: true
        };
      }

      return {
        file,
        metadata,
        processed: false
      };
    } catch (error) {
      console.error('Media processing failed:', error);
      return {
        file,
        metadata: { error: error.message },
        processed: false
      };
    }
  }

  // Fetch template from JSON file or URL
  async fetchTemplate(templateSource) {
    // Check cache first
    if (this.templateCache.has(templateSource)) {
      return this.templateCache.get(templateSource);
    }

    try {
      let template;
      
      if (typeof templateSource === 'string') {
        // Fetch from URL or file path
        const response = await fetch(templateSource);
        if (!response.ok) {
          throw new Error(`Failed to fetch template: ${response.statusText}`);
        }
        template = await response.json();
      } else {
        // Direct template object
        template = templateSource;
      }

      // Validate template structure
      if (!template.renderFunction) {
        throw new Error('Template missing renderFunction');
      }

      // Cache the template
      this.templateCache.set(templateSource, template);
      return template;
    } catch (error) {
      console.error('Failed to fetch template:', error);
      throw error;
    }
  }

  // Execute template with given configuration
  async executeTemplate(templateSource, config = {}, isPlaying = false, onComplete = null) {
    try {
      const template = await this.fetchTemplate(templateSource);
      
      // Merge configuration with defaults
      const mergedConfig = { ...template.default_config, ...config };
      
      // Create execution context
      const context = {
        ...this.renderingContext,
        config: mergedConfig,
        isPlaying,
        onComplete,
        template
      };
      
      // Create a React component that executes the template function
      const TemplateComponent = () => {
        const renderFunction = new Function(
          'return ' + template.renderFunction
        )();
        
        return renderFunction(React, mergedConfig, isPlaying, onComplete, context);
      };
      
      return React.createElement(TemplateComponent);
    } catch (error) {
      console.error('Template execution failed:', error);
      return this.createErrorComponent(error.message);
    }
  }

  // Create error component
  createErrorComponent(errorMessage) {
    return React.createElement('div', {
      style: {
        padding: '20px',
        background: '#fee2e2',
        border: '1px solid #fecaca',
        borderRadius: '8px',
        color: '#dc2626',
        fontFamily: 'monospace'
      }
    }, [
      React.createElement('h3', { key: 'title' }, 'Template Error'),
      React.createElement('p', { key: 'message' }, errorMessage)
    ]);
  }
}

// React component wrapper for Universal Template Renderer
const UniversalTemplateComponent = ({ 
  templateSource, 
  config = {}, 
  isPlaying = false, 
  onComplete = null 
}) => {
  const [component, setComponent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const rendererRef = useRef(new UniversalTemplateRenderer());

  useEffect(() => {
    const loadTemplate = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const renderedComponent = await rendererRef.current.executeTemplate(
          templateSource,
          config,
          isPlaying,
          onComplete
        );
        
        setComponent(renderedComponent);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    loadTemplate();
  }, [templateSource, config, isPlaying, onComplete]);

  if (loading) {
    return React.createElement('div', {
      style: {
        padding: '20px',
        textAlign: 'center',
        color: '#6b7280'
      }
    }, 'Loading template...');
  }

  if (error) {
    return React.createElement('div', {
      style: {
        padding: '20px',
        background: '#fee2e2',
        border: '1px solid #fecaca',
        borderRadius: '8px',
        color: '#dc2626'
      }
    }, `Error: ${error}`);
  }

  return component;
};

export { UniversalTemplateRenderer, UniversalTemplateComponent };
export default UniversalTemplateComponent;