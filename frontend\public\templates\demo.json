{"name": "Demo Money Counter", "description": "A simple money counter animation for demonstration", "version": "1.0.0", "category": "business", "default_config": {"startValue": 0, "endValue": 10000, "duration": 3000, "currency": "$", "fontSize": 48, "color": "#00ff88", "background": "#1a1a2e", "glowEffect": true}, "editable_params": [{"name": "startValue", "label": "Start Value", "type": "number", "min": 0, "max": 1000000}, {"name": "endValue", "label": "End Value", "type": "number", "min": 0, "max": 1000000}, {"name": "duration", "label": "Duration (ms)", "type": "range", "min": 1000, "max": 10000, "step": 100}, {"name": "currency", "label": "Currency Symbol", "type": "text", "max_length": 5}, {"name": "color", "label": "Text Color", "type": "color"}, {"name": "glowEffect", "label": "Glow Effect", "type": "boolean"}], "renderFunction": "function(React, config, isPlaying, onComplete, context) { const { useState, useEffect, useRef } = React; const { createInterval, clearInterval } = context; const [currentValue, setCurrentValue] = useState(config.startValue || 0); const intervalRef = useRef(null); useEffect(() => { if (!isPlaying) { setCurrentValue(config.startValue || 0); if (intervalRef.current) { clearInterval(intervalRef.current); intervalRef.current = null; } return; } const startValue = config.startValue || 0; const endValue = config.endValue || 1000; const duration = config.duration || 3000; const steps = 60; const increment = (endValue - startValue) / (duration / (1000 / steps)); let current = startValue; intervalRef.current = createInterval(() => { current += increment; if (current >= endValue) { current = endValue; setCurrentValue(current); clearInterval(intervalRef.current); intervalRef.current = null; if (onComplete) onComplete(); } else { setCurrentValue(current); } }, 1000 / steps); return () => { if (intervalRef.current) { clearInterval(intervalRef.current); intervalRef.current = null; } }; }, [isPlaying, config]); const formatValue = (value) => { const currency = config.currency || '$'; const decimalPlaces = 0; return currency + value.toFixed(decimalPlaces).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ','); }; const glowStyle = config.glowEffect ? { textShadow: '0 0 20px ' + (config.color || '#00ff88') + ', 0 0 40px ' + (config.color || '#00ff88') } : {}; return React.createElement('div', { style: { color: config.color || '#00ff88', fontSize: (config.fontSize || 48) + 'px', fontWeight: 'bold', background: config.background || '#1a1a2e', padding: '40px', borderRadius: '12px', textAlign: 'center', fontFamily: 'monospace', minHeight: '120px', display: 'flex', alignItems: 'center', justifyContent: 'center', ...glowStyle } }, formatValue(currentValue)); }"}