import os
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import uuid
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import asyncio
from threading import Thread

# Template Auto-Discovery and Management System
class TemplateManager:
    def __init__(self, templates_dir: Path, templates_file: Path):
        self.templates_dir = templates_dir
        self.templates_file = templates_file
        self.templates_cache = {}
        self.file_watcher = None
        self.observer = None
        self.media_type_handlers = {
            'animation': self._validate_animation_template,
            'background': self._validate_background_template,
            'emoji': self._validate_emoji_template,
            'audio': self._validate_audio_template
        }
        self.logger = logging.getLogger(__name__)
        
    def start_file_watcher(self):
        """Start watching the templates directory for changes"""
        if self.observer:
            return
            
        self.file_watcher = TemplateFileHandler(self)
        self.observer = Observer()
        self.observer.schedule(self.file_watcher, str(self.templates_dir), recursive=True)
        self.observer.start()
        self.logger.info(f"Started watching templates directory: {self.templates_dir}")
        
    def stop_file_watcher(self):
        """Stop watching the templates directory"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.observer = None
            self.file_watcher = None
            self.logger.info("Stopped watching templates directory")
    
    def auto_discover_templates(self) -> List[Dict[str, Any]]:
        """Auto-discover all templates from the templates directory"""
        templates = []
        
        if not self.templates_dir.exists():
            self.logger.warning(f"Templates directory does not exist: {self.templates_dir}")
            return templates
            
        # Scan for JSON files
        for template_file in self.templates_dir.glob("*.json"):
            if template_file.name == "index.json":
                continue
                
            try:
                template = self._load_template_file(template_file)
                if template:
                    templates.append(template)
                    self.logger.info(f"Discovered template: {template.get('name', 'Unknown')} ({template.get('type', 'Unknown')})")
            except Exception as e:
                self.logger.error(f"Failed to load template from {template_file}: {e}")
                
        # Cache the templates
        self.templates_cache = {t['id']: t for t in templates}
        
        # Update the main templates.json file for backward compatibility
        self._update_templates_file(templates)
        
        self.logger.info(f"Auto-discovered {len(templates)} templates")
        return templates
    
    def _load_template_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Load and validate a single template file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)
                
            # Validate and enhance template
            template = self._validate_and_enhance_template(template_data, file_path)
            return template
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in {file_path}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error loading template from {file_path}: {e}")
            return None
    
    def _validate_and_enhance_template(self, template_data: Dict[str, Any], file_path: Path) -> Dict[str, Any]:
        """Validate and enhance template data"""
        template = template_data.copy()
        
        # Ensure required fields
        if 'id' not in template:
            template['id'] = str(uuid.uuid4())
            self.logger.info(f"Generated ID for template: {template['id']}")
            
        if 'created_at' not in template:
            template['created_at'] = datetime.utcnow().isoformat()
            
        # Auto-detect media type if not specified
        if 'media_type' not in template:
            template['media_type'] = self._detect_media_type(template, file_path)
            
        # Auto-detect template type if not specified
        if 'type' not in template:
            template['type'] = self._detect_template_type(template, file_path)
            
        # Convert modern config format to legacy format if needed
        if 'config' in template and 'editable_params' not in template:
            template = self._convert_config_to_legacy_format(template)
            
        # Validate based on media type
        media_type = template.get('media_type', 'animation')
        if media_type in self.media_type_handlers:
            template = self.media_type_handlers[media_type](template)
            
        # Auto-generate editable params if not provided
        if 'editable_params' not in template or not template['editable_params']:
            template['editable_params'] = self._generate_editable_params(template)
            
        return template
    
    def _convert_config_to_legacy_format(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """Convert modern config format to legacy editable_params and default_config format"""
        config = template.get('config', {})
        
        # Convert config to editable_params
        editable_params = []
        default_config = {}
        
        for name, param_def in config.items():
            if isinstance(param_def, dict) and 'type' in param_def:
                # Create editable param
                editable_param = {
                    'name': name,
                    'type': param_def['type'],
                    'label': param_def.get('label', name.replace('_', ' ').title())
                }
                
                # Add optional properties
                for prop in ['min', 'max', 'step', 'options', 'placeholder']:
                    if prop in param_def:
                        editable_param[prop] = param_def[prop]
                        
                editable_params.append(editable_param)
                
                # Set default value
                if 'default' in param_def:
                    default_config[name] = param_def['default']
        
        # Use preview_config if available, otherwise use extracted defaults
        if 'preview_config' in template:
            template['default_config'] = template['preview_config']
        else:
            template['default_config'] = default_config
            
        template['editable_params'] = editable_params
        
        # Keep the original config for reference but don't let it interfere
        # with the legacy format processing
        return template
    
    def _detect_media_type(self, template: Dict[str, Any], file_path: Path) -> str:
        """Auto-detect media type based on template content and filename"""
        filename = file_path.stem.lower()
        template_type = template.get('type', '').lower()
        config = template.get('default_config', {})
        
        # Check filename patterns
        if any(keyword in filename for keyword in ['background', 'bg', 'wallpaper']):
            return 'background'
        elif any(keyword in filename for keyword in ['emoji', 'emoticon', 'smiley']):
            return 'emoji'
        elif any(keyword in filename for keyword in ['audio', 'sound', 'music']):
            return 'audio'
            
        # Check template type patterns
        if any(keyword in template_type for keyword in ['background', 'wallpaper']):
            return 'background'
        elif any(keyword in template_type for keyword in ['emoji', 'emoticon']):
            return 'emoji'
        elif any(keyword in template_type for keyword in ['audio', 'sound']):
            return 'audio'
            
        # Check config for media-specific properties
        if any(key in config for key in ['audio_url', 'sound_file', 'volume']):
            return 'audio'
        elif any(key in config for key in ['emoji', 'unicode', 'emoticon']):
            return 'emoji'
        elif any(key in config for key in ['background_image', 'gradient', 'pattern']):
            return 'background'
            
        # Default to animation
        return 'animation'
    
    def _detect_template_type(self, template: Dict[str, Any], file_path: Path) -> str:
        """Auto-detect template type based on content and filename"""
        filename = file_path.stem.lower()
        config = template.get('default_config', {})
        
        # Common type patterns
        type_patterns = {
            'counter': ['counter', 'count', 'number', 'money'],
            'chart': ['chart', 'graph', 'bar', 'pie', 'data'],
            'text_animation': ['text', 'typography', 'typewriter', 'font'],
            'progress_bar': ['progress', 'bar', 'loading'],
            'particles': ['particle', 'burst', 'explosion', 'effect'],
            'social_counter': ['social', 'follower', 'like', 'instagram', 'youtube'],
            'countdown': ['countdown', 'timer', 'clock'],
            'logo_reveal': ['logo', 'brand', 'reveal'],
            'loading': ['loading', 'spinner', 'loader'],
            'alert_banner': ['alert', 'banner', 'warning', 'notification'],
            'cta_button': ['button', 'cta', 'call', 'action'],
            'ui_notification': ['notification', 'popup', 'toast'],
            'geo_animation': ['map', 'geo', 'location', 'pin']
        }
        
        # Check filename against patterns
        for template_type, keywords in type_patterns.items():
            if any(keyword in filename for keyword in keywords):
                return template_type
                
        # Check config properties
        if any(key in config for key in ['start_value', 'end_value', 'currency']):
            return 'counter'
        elif any(key in config for key in ['data', 'bars', 'chart_type']):
            return 'chart'
        elif any(key in config for key in ['text', 'font_size', 'typewriter']):
            return 'text_animation'
        elif any(key in config for key in ['progress', 'percentage']):
            return 'progress_bar'
        elif any(key in config for key in ['particle_count', 'particle_color']):
            return 'particles'
            
        # Default to generic animation
        return 'generic_animation'
    
    def _generate_editable_params(self, template: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Auto-generate editable parameters based on default config"""
        config = template.get('default_config', {})
        params = []
        
        # Common parameter mappings
        param_mappings = {
            'color': {'type': 'color', 'label': 'Color'},
            'background': {'type': 'color', 'label': 'Background Color'},
            'text_color': {'type': 'color', 'label': 'Text Color'},
            'font_size': {'type': 'range', 'label': 'Font Size', 'min': 12, 'max': 72, 'step': 2},
            'duration': {'type': 'range', 'label': 'Duration (ms)', 'min': 500, 'max': 10000, 'step': 100},
            'text': {'type': 'text', 'label': 'Text Content'},
            'title': {'type': 'text', 'label': 'Title'},
            'message': {'type': 'text', 'label': 'Message'},
            'width': {'type': 'range', 'label': 'Width', 'min': 100, 'max': 800, 'step': 10},
            'height': {'type': 'range', 'label': 'Height', 'min': 50, 'max': 400, 'step': 10},
            'opacity': {'type': 'range', 'label': 'Opacity', 'min': 0, 'max': 1, 'step': 0.1},
            'speed': {'type': 'range', 'label': 'Speed', 'min': 0.1, 'max': 3, 'step': 0.1},
            'size': {'type': 'range', 'label': 'Size', 'min': 10, 'max': 100, 'step': 5}
        }
        
        for key, value in config.items():
            if key in param_mappings:
                param = {'name': key, **param_mappings[key]}
                params.append(param)
            elif isinstance(value, bool):
                params.append({
                    'name': key,
                    'type': 'boolean',
                    'label': key.replace('_', ' ').title()
                })
            elif isinstance(value, (int, float)):
                params.append({
                    'name': key,
                    'type': 'number',
                    'label': key.replace('_', ' ').title(),
                    'min': 0
                })
            elif isinstance(value, str) and len(value) < 100:
                params.append({
                    'name': key,
                    'type': 'text',
                    'label': key.replace('_', ' ').title()
                })
                
        return params
    
    def _validate_animation_template(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """Validate animation-specific template properties"""
        config = template.get('default_config', {})
        
        # Only add duration if no timing-related parameters exist
        if not any(key in config for key in ['duration', 'speed', 'delay', 'timing']):
            config['duration'] = 3000
            
        # Only add background if no color/styling parameters exist
        if not any(key in config for key in ['background', 'background_color', 'bg_color', 'color', 'palette']):
            config['background'] = '#000000'
            
        template['default_config'] = config
        return template
    
    def _validate_background_template(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """Validate background-specific template properties"""
        config = template.get('default_config', {})
        
        # Ensure background properties
        if not any(key in config for key in ['background', 'gradient', 'pattern', 'image_url']):
            config['background'] = '#f0f0f0'
            
        template['default_config'] = config
        return template
    
    def _validate_emoji_template(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """Validate emoji-specific template properties"""
        config = template.get('default_config', {})
        
        # Ensure emoji property
        if 'emoji' not in config:
            config['emoji'] = '😀'
            
        # Ensure size
        if 'size' not in config:
            config['size'] = '48px'
            
        template['default_config'] = config
        return template
    
    def _validate_audio_template(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """Validate audio-specific template properties"""
        config = template.get('default_config', {})
        
        # Ensure audio properties
        if 'volume' not in config:
            config['volume'] = 1.0
            
        if 'loop' not in config:
            config['loop'] = False
            
        template['default_config'] = config
        return template
    
    def _update_templates_file(self, templates: List[Dict[str, Any]]):
        """Update the main templates.json file for backward compatibility"""
        try:
            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump(templates, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Updated templates file: {self.templates_file}")
        except Exception as e:
            self.logger.error(f"Failed to update templates file: {e}")
    
    def create_template_from_config(self, name: str, template_type: str, config: Dict[str, Any], 
                                  category: str = 'custom', media_type: str = 'animation') -> Dict[str, Any]:
        """Create a new template from configuration"""
        template = {
            'id': str(uuid.uuid4()),
            'name': name,
            'type': template_type,
            'category': category,
            'media_type': media_type,
            'description': f'Auto-generated {template_type} template',
            'default_config': config,
            'created_at': datetime.utcnow().isoformat()
        }
        
        # Validate and enhance
        template = self._validate_and_enhance_template(template, Path(f"{name}.json"))
        
        # Save to file
        template_file = self.templates_dir / f"{name.lower().replace(' ', '_')}.json"
        try:
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(template, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Created new template file: {template_file}")
        except Exception as e:
            self.logger.error(f"Failed to save template file: {e}")
            
        return template
    
    def get_template_by_id(self, template_id: str) -> Optional[Dict[str, Any]]:
        """Get a template by ID from cache"""
        return self.templates_cache.get(template_id)
    
    def get_templates_by_type(self, template_type: str) -> List[Dict[str, Any]]:
        """Get all templates of a specific type"""
        return [t for t in self.templates_cache.values() if t.get('type') == template_type]
    
    def get_templates_by_media_type(self, media_type: str) -> List[Dict[str, Any]]:
        """Get all templates of a specific media type"""
        return [t for t in self.templates_cache.values() if t.get('media_type', 'animation') == media_type]


class TemplateFileHandler(FileSystemEventHandler):
    """File system event handler for template files"""
    
    def __init__(self, template_manager: TemplateManager):
        self.template_manager = template_manager
        self.logger = logging.getLogger(__name__)
    
    def on_created(self, event):
        if not event.is_directory and event.src_path.endswith('.json'):
            self.logger.info(f"New template file detected: {event.src_path}")
            # Reload templates after a short delay
            Thread(target=self._delayed_reload, daemon=True).start()
    
    def on_modified(self, event):
        if not event.is_directory and event.src_path.endswith('.json'):
            self.logger.info(f"Template file modified: {event.src_path}")
            Thread(target=self._delayed_reload, daemon=True).start()
    
    def on_deleted(self, event):
        if not event.is_directory and event.src_path.endswith('.json'):
            self.logger.info(f"Template file deleted: {event.src_path}")
            Thread(target=self._delayed_reload, daemon=True).start()
    
    def _delayed_reload(self):
        """Reload templates after a short delay to avoid multiple rapid reloads"""
        import time
        time.sleep(1)  # Wait 1 second
        self.template_manager.auto_discover_templates()


# Template creation utilities
def create_animation_template(name: str, animation_type: str, config: Dict[str, Any]) -> Dict[str, Any]:
    """Utility function to create an animation template"""
    return {
        'id': str(uuid.uuid4()),
        'name': name,
        'type': animation_type,
        'category': 'custom',
        'media_type': 'animation',
        'description': f'Custom {animation_type} animation',
        'default_config': config,
        'created_at': datetime.utcnow().isoformat()
    }

def create_background_template(name: str, background_config: Dict[str, Any]) -> Dict[str, Any]:
    """Utility function to create a background template"""
    return {
        'id': str(uuid.uuid4()),
        'name': name,
        'type': 'background',
        'category': 'backgrounds',
        'media_type': 'background',
        'description': f'Custom background: {name}',
        'default_config': background_config,
        'created_at': datetime.utcnow().isoformat()
    }

def create_emoji_template(name: str, emoji: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Utility function to create an emoji template"""
    default_config = {'emoji': emoji, 'size': '48px', 'duration': 1000}
    if config:
        default_config.update(config)
        
    return {
        'id': str(uuid.uuid4()),
        'name': name,
        'type': 'emoji_animation',
        'category': 'emojis',
        'media_type': 'emoji',
        'description': f'Emoji animation: {emoji}',
        'default_config': default_config,
        'created_at': datetime.utcnow().isoformat()
    }

def create_audio_template(name: str, audio_url: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Utility function to create an audio template"""
    default_config = {'audio_url': audio_url, 'volume': 1.0, 'loop': False}
    if config:
        default_config.update(config)
        
    return {
        'id': str(uuid.uuid4()),
        'name': name,
        'type': 'audio_clip',
        'category': 'audio',
        'media_type': 'audio',
        'description': f'Audio clip: {name}',
        'default_config': default_config,
        'created_at': datetime.utcnow().isoformat()
    }