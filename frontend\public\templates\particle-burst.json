{"id": "particle-burst", "type": "particle_effect", "name": "Particle Burst", "description": "Animated particle explosion effect", "version": "1.0.0", "category": "creative", "default_config": {"particleCount": 50, "duration": 3000, "particleColor": "#fbbf24", "particleSize": 4, "spread": 200, "gravity": 0.5, "background": "#000000", "triggerDelay": 0, "particleShape": "circle", "trailEffect": false}, "editable_params": [{"name": "particleCount", "label": "Particle Count", "type": "range", "min": 10, "max": 200, "step": 5}, {"name": "duration", "label": "Duration (ms)", "type": "range", "min": 1000, "max": 8000, "step": 200}, {"name": "particleColor", "label": "Particle Color", "type": "color"}, {"name": "particleSize", "label": "Particle Size", "type": "range", "min": 2, "max": 10, "step": 1}, {"name": "spread", "label": "Spread", "type": "range", "min": 50, "max": 400, "step": 10}, {"name": "gravity", "label": "Gravity", "type": "range", "min": 0, "max": 2, "step": 0.1}, {"name": "background", "label": "Background Color", "type": "color"}, {"name": "trigger<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON> (ms)", "type": "number", "min": 0, "max": 5000, "default": 0}, {"name": "particleShape", "label": "Particle Shape", "type": "select", "default": "circle", "options": ["circle", "square", "triangle"]}, {"name": "trailEffect", "label": "Trail Effect", "type": "boolean", "default": false}], "renderFunction": "function(React, config, isPlaying, onComplete, context) { const { useState, useEffect, useRef } = React; const [particles, setParticles] = useState([]); const containerRef = useRef(null); useEffect(() => { if (!isPlaying) { setParticles([]); return; } const particleArray = []; const centerX = 200; const centerY = 200; for (let i = 0; i < (config.particleCount || 50); i++) { const angle = (Math.PI * 2 * i) / (config.particleCount || 50); const velocity = Math.random() * (config.spread || 200) / 10; particleArray.push({ id: i, x: centerX, y: centerY, vx: Math.cos(angle) * velocity, vy: Math.sin(angle) * velocity, size: config.particleSize || 4, color: config.particleColor || '#fbbf24', life: 1 }); } setParticles(particleArray); const animationDuration = config.duration || 3000; const gravity = config.gravity || 0.5; const startTime = Date.now(); const animate = () => { const elapsed = Date.now() - startTime; const progress = elapsed / animationDuration; if (progress >= 1) { setParticles([]); if (onComplete) onComplete(); return; } setParticles(prev => prev.map(particle => ({ ...particle, x: particle.x + particle.vx, y: particle.y + particle.vy + gravity * (elapsed / 100), vy: particle.vy + gravity * 0.1, life: 1 - progress }))); requestAnimationFrame(animate); }; requestAnimationFrame(animate); }, [isPlaying, config]); const containerStyle = { width: '400px', height: '400px', background: config.background || '#000000', position: 'relative', overflow: 'hidden', borderRadius: '12px', margin: '20px auto' }; return React.createElement('div', { ref: containerRef, style: containerStyle }, particles.map(particle => React.createElement('div', { key: particle.id, style: { position: 'absolute', left: particle.x + 'px', top: particle.y + 'px', width: particle.size + 'px', height: particle.size + 'px', backgroundColor: particle.color, borderRadius: '50%', opacity: particle.life, transform: 'translate(-50%, -50%)' } }))); }"}