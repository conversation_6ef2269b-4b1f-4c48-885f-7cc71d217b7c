import React, { useState, useEffect } from 'react';
import './App.css';
import TemplateRenderer from './components/TemplateRenderer';
import TemplateGallery from './components/TemplateGallery';
import ParameterEditor from './components/ParameterEditor';

// App component using SelfContainedTemplateEngine
const App = () => {
  const [template, setTemplate] = useState(null);
  const [config, setConfig] = useState({});
  const [isPlaying, setIsPlaying] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showGallery, setShowGallery] = useState(false);
  const [showParameters, setShowParameters] = useState(true);


  useEffect(() => {
    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const templateParam = urlParams.get('template');
    const configParam = urlParams.get('config');
    const autoplay = urlParams.get('autoplay') === 'true';

    const loadTemplate = async () => {
      try {
        let templateData;
        if (templateParam) {
          // Load template from backend API
          const response = await fetch(`http://localhost:8001/api/templates/${templateParam}`);
          if (response.ok) {
            templateData = await response.json();
          } else {
            // Fallback to frontend templates
            const frontendResponse = await fetch(`/templates/${templateParam}.json`);
            if (frontendResponse.ok) {
              templateData = await frontendResponse.json();
            }
          }
        }
        
        if (templateData) {
          setTemplate(templateData);
          setShowGallery(false);
          
          // Initialize config with default values
          const initialConfig = { ...templateData.default_config };
          
          // Parse config if provided
          if (configParam) {
            try {
              const parsedConfig = JSON.parse(decodeURIComponent(configParam));
              Object.assign(initialConfig, parsedConfig);
            } catch (error) {
              console.error('Invalid config parameter:', error);
            }
          }
          
          setConfig(initialConfig);
          
          // Auto-play if specified
          if (autoplay) {
            setIsPlaying(true);
          }
        } else {
          // No template specified, show gallery
          setShowGallery(true);
        }
      } catch (error) {
        console.error('Error loading template:', error);
        setShowGallery(true);
      } finally {
        setLoading(false);
      }
    };
    
    loadTemplate();
  }, []);

  const handleComplete = () => {
    console.log('Animation completed!');
    setIsPlaying(false);
  };

  const handlePlay = () => {
    setIsPlaying(true);
  };

  const handleStop = () => {
    setIsPlaying(false);
  };

  const handleConfigChange = (newConfig) => {
    setConfig(newConfig);
    // Auto-restart animation if it's currently playing
    if (isPlaying) {
      setIsPlaying(false);
      setTimeout(() => setIsPlaying(true), 100);
    }
  };

  const toggleParameters = () => {
    setShowParameters(!showParameters);
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <div style={{ textAlign: 'center', color: 'white' }}>
          <div style={{
            width: '50px',
            height: '50px',
            border: '4px solid rgba(255,255,255,0.3)',
            borderTop: '4px solid white',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 20px'
          }}></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (showGallery) {
    return <TemplateGallery />;
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        <header style={{
          textAlign: 'center',
          marginBottom: '40px',
          color: 'white'
        }}>
          <h1 style={{
            fontSize: '3rem',
            fontWeight: '700',
            marginBottom: '1rem',
            textShadow: '0 4px 20px rgba(0,0,0,0.3)'
          }}>
            Motion Stock
          </h1>
          <p style={{
            fontSize: '1.2rem',
            opacity: 0.9,
            marginBottom: '2rem'
          }}>
            Self-contained template animation system
          </p>
        </header>

        {/* Controls */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          gap: '15px',
          marginBottom: '30px'
        }}>
          <button
            onClick={handlePlay}
            disabled={isPlaying}
            style={{
              background: isPlaying ? '#6b7280' : 'linear-gradient(135deg, #10b981, #059669)',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '25px',
              fontWeight: '600',
              cursor: isPlaying ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: '0 4px 15px rgba(16, 185, 129, 0.4)'
            }}
          >
            {isPlaying ? 'Playing...' : 'Play Animation'}
          </button>
          
          <button
            onClick={handleStop}
            disabled={!isPlaying}
            style={{
              background: !isPlaying ? '#6b7280' : 'linear-gradient(135deg, #ef4444, #dc2626)',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '25px',
              fontWeight: '600',
              cursor: !isPlaying ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: '0 4px 15px rgba(239, 68, 68, 0.4)'
            }}
          >
            Stop
          </button>
          
          <button
            onClick={toggleParameters}
            style={{
              background: showParameters ? 'linear-gradient(135deg, #f59e0b, #d97706)' : 'linear-gradient(135deg, #6366f1, #4f46e5)',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '25px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: showParameters ? '0 4px 15px rgba(245, 158, 11, 0.4)' : '0 4px 15px rgba(99, 102, 241, 0.4)'
            }}
          >
            {showParameters ? 'Hide Parameters' : 'Show Parameters'}
          </button>
          
          <button
            onClick={() => window.location.href = '/'}
            style={{
              background: 'linear-gradient(135deg, #8b5cf6, #a855f7)',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '25px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: '0 4px 15px rgba(139, 92, 246, 0.4)'
            }}
          >
            ← Back to Gallery
          </button>
        </div>

        {/* Parameter Editor */}
        {template && showParameters && (
          <ParameterEditor
            template={template}
            config={config}
            onConfigChange={handleConfigChange}
          />
        )}

        {/* Animation Display */}
        <div style={{
          background: '#1e293b',
          borderRadius: '12px',
          padding: '20px',
          minHeight: '400px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          {template ? (
            <TemplateRenderer
              template={template}
              config={config}
              isPlaying={isPlaying}
              onComplete={handleComplete}
            />
          ) : (
            <div style={{
              textAlign: 'center',
              color: '#64748b'
            }}>
              <p>No template specified</p>
              <p style={{ fontSize: '0.9rem', marginTop: '10px' }}>
                Add ?template=templateName to the URL
              </p>
            </div>
          )}
        </div>

        <footer style={{
          textAlign: 'center',
          marginTop: '40px',
          color: '#64748b',
          fontSize: '0.9rem'
        }}>
          <p>Motion Stock • Self-Contained Template System</p>
          <p style={{ marginTop: '5px' }}>
            URL Parameters: ?template=name&config=json&autoplay=true
          </p>
        </footer>
      </div>
    </div>
  );
};

export default App;