import React from 'react';
import { enhancedAnimationRegistry } from './EnhancedAnimationRegistry';

// Enhanced Template Loader with Smart Component Resolution
class EnhancedTemplateLoader {
  constructor() {
    this.templates = new Map();
    this.loadingStates = new Map();
    this.errorStates = new Map();
    this.debugMode = process.env.NODE_ENV === 'development';
    this.retryAttempts = new Map();
    this.maxRetries = 3;
    
    // Initialize template discovery
    this.initializeTemplateDiscovery();
  }

  async initializeTemplateDiscovery() {
    try {
      await this.discoverTemplates();
      this.log('Template discovery completed');
    } catch (error) {
      this.logError('Failed to initialize template discovery', error);
    }
  }

  // Enhanced template discovery with better error handling
  async discoverTemplates() {
    try {
      this.log('Starting enhanced template discovery...');
      
      const response = await fetch('/api/templates');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!data.templates || !Array.isArray(data.templates)) {
        throw new Error('Invalid template data structure received');
      }
      
      let processedCount = 0;
      let errorCount = 0;
      
      for (const template of data.templates) {
        try {
          const processedTemplate = await this.processTemplate(template);
          this.templates.set(template.id, processedTemplate);
          processedCount++;
        } catch (error) {
          this.logError(`Failed to process template ${template.id}`, error);
          errorCount++;
        }
      }
      
      this.log(`Template discovery completed: ${processedCount} processed, ${errorCount} errors`);
      
      // Auto-register components for discovered templates
      await this.autoRegisterDiscoveredComponents();
      
      return { processedCount, errorCount, total: data.templates.length };
    } catch (error) {
      this.logError('Template discovery failed', error);
      throw error;
    }
  }

  // Enhanced template processing with validation
  async processTemplate(template) {
    // Validate required fields
    const requiredFields = ['id', 'name', 'type'];
    for (const field of requiredFields) {
      if (!template[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    // Normalize template structure
    const processedTemplate = {
      ...template,
      media_type: template.media_type || 'animation',
      default_config: this.processConfig(template.config || template.preview_config || {}),
      editable_params: this.generateEditableParams(template.config || template.preview_config || {}),
      component_status: 'unknown',
      processing_timestamp: new Date().toISOString()
    };
    
    // Check component availability
    const hasComponent = enhancedAnimationRegistry.has(template.type, processedTemplate.media_type);
    processedTemplate.component_status = hasComponent ? 'registered' : 'needs_registration';
    
    // Generate component suggestions if not registered
    if (!hasComponent) {
      processedTemplate.suggested_patterns = enhancedAnimationRegistry.getSuggestedPatterns(template.type);
      processedTemplate.similar_types = this.findSimilarRegisteredTypes(template.type);
    }
    
    this.log(`Processed template: ${template.id} (${template.type}) - Status: ${processedTemplate.component_status}`);
    
    return processedTemplate;
  }

  // Process and validate configuration
  processConfig(config) {
    if (!config || typeof config !== 'object') {
      return {};
    }
    
    const processedConfig = { ...config };
    
    // Add default values for common parameters
    if (!processedConfig.duration && typeof processedConfig.duration !== 'number') {
      processedConfig.duration = 3000; // 3 seconds default
    }
    
    // Validate numeric values
    Object.keys(processedConfig).forEach(key => {
      const value = processedConfig[key];
      if (typeof value === 'string' && !isNaN(value) && value.trim() !== '') {
        processedConfig[key] = parseFloat(value);
      }
    });
    
    return processedConfig;
  }

  // Generate editable parameters with enhanced metadata
  generateEditableParams(config) {
    if (!config || typeof config !== 'object') {
      return [];
    }
    
    return Object.entries(config).map(([key, value]) => {
      const param = {
        name: key,
        value: value,
        type: this.inferParameterType(key, value),
        category: this.categorizeParameter(key),
        description: this.generateParameterDescription(key, value)
      };
      
      // Add constraints based on parameter type
      switch (param.type) {
        case 'number':
          param.min = this.getParameterMin(key);
          param.max = this.getParameterMax(key);
          param.step = this.getParameterStep(key);
          break;
        case 'color':
          param.format = 'hex';
          break;
        case 'select':
          param.options = this.getParameterOptions(key);
          break;
      }
      
      return param;
    });
  }

  // Infer parameter type from key and value
  inferParameterType(key, value) {
    const lowerKey = key.toLowerCase();
    
    // Color detection
    if (lowerKey.includes('color') || lowerKey.includes('colour')) {
      return 'color';
    }
    
    // Boolean detection
    if (typeof value === 'boolean' || lowerKey.startsWith('is_') || lowerKey.startsWith('show_') || lowerKey.startsWith('enable_')) {
      return 'boolean';
    }
    
    // Number detection
    if (typeof value === 'number' || lowerKey.includes('count') || lowerKey.includes('size') || 
        lowerKey.includes('width') || lowerKey.includes('height') || lowerKey.includes('duration') ||
        lowerKey.includes('speed') || lowerKey.includes('delay') || lowerKey.includes('value')) {
      return 'number';
    }
    
    // Select/enum detection
    if (lowerKey.includes('type') || lowerKey.includes('style') || lowerKey.includes('mode') ||
        lowerKey.includes('align') || lowerKey.includes('position')) {
      return 'select';
    }
    
    // Range detection
    if (lowerKey.includes('opacity') || lowerKey.includes('progress') || lowerKey.includes('percentage')) {
      return 'range';
    }
    
    // Default to text
    return 'text';
  }

  // Categorize parameters for better organization
  categorizeParameter(key) {
    const lowerKey = key.toLowerCase();
    
    if (lowerKey.includes('color') || lowerKey.includes('background') || lowerKey.includes('border')) {
      return 'appearance';
    }
    
    if (lowerKey.includes('duration') || lowerKey.includes('speed') || lowerKey.includes('delay') || lowerKey.includes('interval')) {
      return 'timing';
    }
    
    if (lowerKey.includes('width') || lowerKey.includes('height') || lowerKey.includes('size') || lowerKey.includes('position')) {
      return 'layout';
    }
    
    if (lowerKey.includes('text') || lowerKey.includes('font') || lowerKey.includes('content') || lowerKey.includes('label')) {
      return 'content';
    }
    
    if (lowerKey.includes('count') || lowerKey.includes('value') || lowerKey.includes('amount')) {
      return 'data';
    }
    
    return 'general';
  }

  // Generate human-readable parameter descriptions
  generateParameterDescription(key, value) {
    const lowerKey = key.toLowerCase();
    const type = this.inferParameterType(key, value);
    
    const descriptions = {
      duration: 'Animation duration in milliseconds',
      speed: 'Animation speed multiplier',
      delay: 'Delay before animation starts (ms)',
      color: 'Color value (hex, rgb, or color name)',
      background: 'Background color or gradient',
      font_size: 'Font size in pixels or relative units',
      width: 'Width in pixels or percentage',
      height: 'Height in pixels or percentage',
      opacity: 'Transparency level (0-1)',
      count: 'Number of elements or iterations',
      text: 'Text content to display',
      start_value: 'Starting value for counters',
      end_value: 'Ending value for counters',
      particle_count: 'Number of particles to generate',
      bar_count: 'Number of bars in visualization'
    };
    
    if (descriptions[lowerKey]) {
      return descriptions[lowerKey];
    }
    
    // Generate description based on type and key
    const keyWords = key.split('_').join(' ');
    switch (type) {
      case 'boolean':
        return `Enable or disable ${keyWords}`;
      case 'number':
        return `Numeric value for ${keyWords}`;
      case 'color':
        return `Color setting for ${keyWords}`;
      case 'select':
        return `Selection option for ${keyWords}`;
      default:
        return `Configuration for ${keyWords}`;
    }
  }

  // Get parameter constraints
  getParameterMin(key) {
    const constraints = {
      duration: 100,
      delay: 0,
      opacity: 0,
      speed: 0.1,
      count: 1,
      size: 1,
      width: 1,
      height: 1,
      font_size: 8
    };
    
    return constraints[key.toLowerCase()] || 0;
  }

  getParameterMax(key) {
    const constraints = {
      duration: 30000,
      delay: 10000,
      opacity: 1,
      speed: 10,
      count: 1000,
      size: 1000,
      width: 2000,
      height: 2000,
      font_size: 200
    };
    
    return constraints[key.toLowerCase()] || 1000;
  }

  getParameterStep(key) {
    const steps = {
      duration: 100,
      delay: 50,
      opacity: 0.1,
      speed: 0.1,
      font_size: 1
    };
    
    return steps[key.toLowerCase()] || 1;
  }

  getParameterOptions(key) {
    const options = {
      text_align: ['left', 'center', 'right', 'justify'],
      font_weight: ['normal', 'bold', '100', '200', '300', '400', '500', '600', '700', '800', '900'],
      animation_type: ['fade', 'slide', 'bounce', 'zoom', 'rotate'],
      chart_type: ['bar', 'line', 'pie', 'area', 'scatter'],
      position: ['top', 'bottom', 'left', 'right', 'center'],
      style: ['solid', 'dashed', 'dotted', 'gradient']
    };
    
    return options[key.toLowerCase()] || [];
  }

  // Find similar registered types for suggestions
  findSimilarRegisteredTypes(templateType) {
    const registeredTypes = enhancedAnimationRegistry.getRegisteredTypes();
    const similarities = [];
    
    for (const registeredType of registeredTypes) {
      const similarity = this.calculateSimilarity(templateType, registeredType);
      if (similarity > 0.3) { // 30% similarity threshold
        similarities.push({ type: registeredType, similarity });
      }
    }
    
    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, 3)
      .map(item => item.type);
  }

  // Calculate string similarity (reuse from registry)
  calculateSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const distance = this.levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  }

  levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  // Auto-register components for discovered templates
  async autoRegisterDiscoveredComponents() {
    let registeredCount = 0;
    
    for (const [templateId, template] of this.templates.entries()) {
      if (template.component_status === 'needs_registration') {
        try {
          const component = await enhancedAnimationRegistry.get(
            template.type, 
            template.media_type, 
            template
          );
          
          if (component) {
            template.component_status = 'auto_registered';
            registeredCount++;
            this.log(`Auto-registered component for template: ${templateId} (${template.type})`);
          }
        } catch (error) {
          this.logError(`Failed to auto-register component for template ${templateId}`, error);
          template.component_status = 'registration_failed';
        }
      }
    }
    
    this.log(`Auto-registration completed: ${registeredCount} components registered`);
    return registeredCount;
  }

  // Enhanced component retrieval with retry logic
  async getComponentForTemplate(template, retryCount = 0) {
    const templateId = template.id || 'unknown';
    
    try {
      this.setLoadingState(templateId, true);
      this.clearErrorState(templateId);
      
      // Get component from enhanced registry
      const component = await enhancedAnimationRegistry.get(
        template.type,
        template.media_type || 'animation',
        template
      );
      
      if (!component) {
        throw new Error(`No component available for type: ${template.type}`);
      }
      
      this.setLoadingState(templateId, false);
      this.resetRetryCount(templateId);
      
      return component;
    } catch (error) {
      this.setLoadingState(templateId, false);
      this.setErrorState(templateId, error.message);
      
      // Retry logic
      if (retryCount < this.maxRetries) {
        this.log(`Retrying component retrieval for ${templateId} (attempt ${retryCount + 1})`);
        await this.delay(1000 * (retryCount + 1)); // Exponential backoff
        return this.getComponentForTemplate(template, retryCount + 1);
      }
      
      this.logError(`Failed to get component for template ${templateId} after ${this.maxRetries} retries`, error);
      throw error;
    }
  }

  // Enhanced template rendering with error boundaries
  renderTemplate(template, config = {}, isPlaying = false, onComplete = null) {
    const templateId = template.id || 'unknown';
    
    return React.createElement(EnhancedTemplateRenderer, {
      key: templateId,
      template,
      config,
      isPlaying,
      onComplete,
      loader: this
    });
  }

  // State management helpers
  setLoadingState(templateId, isLoading) {
    this.loadingStates.set(templateId, isLoading);
  }

  getLoadingState(templateId) {
    return this.loadingStates.get(templateId) || false;
  }

  setErrorState(templateId, error) {
    this.errorStates.set(templateId, error);
  }

  getErrorState(templateId) {
    return this.errorStates.get(templateId) || null;
  }

  clearErrorState(templateId) {
    this.errorStates.delete(templateId);
  }

  resetRetryCount(templateId) {
    this.retryAttempts.delete(templateId);
  }

  // Utility methods
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  log(message) {
    if (this.debugMode) {
      console.log(`[EnhancedTemplateLoader] ${message}`);
    }
  }

  logError(message, error = null) {
    console.error(`[EnhancedTemplateLoader] ${message}`, error);
  }

  // Public API methods
  getAllTemplates() {
    return Array.from(this.templates.values());
  }

  getTemplate(templateId) {
    return this.templates.get(templateId);
  }

  getTemplatesByType(type) {
    return this.getAllTemplates().filter(template => template.type === type);
  }

  getTemplatesByCategory(category) {
    return this.getAllTemplates().filter(template => template.category === category);
  }

  getTemplatesWithStatus(status) {
    return this.getAllTemplates().filter(template => template.component_status === status);
  }

  // Get loader status for debugging
  getStatus() {
    const templates = this.getAllTemplates();
    const statusCounts = {};
    
    templates.forEach(template => {
      const status = template.component_status;
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });
    
    return {
      totalTemplates: templates.length,
      statusCounts,
      loadingStates: Object.fromEntries(this.loadingStates),
      errorStates: Object.fromEntries(this.errorStates),
      retryAttempts: Object.fromEntries(this.retryAttempts)
    };
  }
}

// Enhanced Template Renderer Component with Error Boundary
class EnhancedTemplateRenderer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      component: null,
      loading: true,
      error: null,
      hasError: false
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error: error.message };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Enhanced Template Renderer Error:', error, errorInfo);
  }

  async componentDidMount() {
    await this.loadComponent();
  }

  async componentDidUpdate(prevProps) {
    if (prevProps.template.id !== this.props.template.id ||
        prevProps.template.type !== this.props.template.type) {
      await this.loadComponent();
    }
  }

  async loadComponent() {
    try {
      this.setState({ loading: true, error: null, hasError: false });
      
      const component = await this.props.loader.getComponentForTemplate(this.props.template);
      
      this.setState({ 
        component, 
        loading: false, 
        error: null 
      });
    } catch (error) {
      this.setState({ 
        loading: false, 
        error: error.message,
        component: null 
      });
    }
  }

  render() {
    const { template, config, isPlaying, onComplete } = this.props;
    const { component: Component, loading, error, hasError } = this.state;

    if (hasError) {
      return (
        <div className="template-error-boundary" style={{
          padding: '20px',
          border: '2px solid #ef4444',
          borderRadius: '8px',
          background: '#fef2f2',
          color: '#dc2626'
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>🚨 Rendering Error</div>
          <div>Template: {template.name} ({template.type})</div>
          <div style={{ fontSize: '12px', marginTop: '8px', opacity: 0.8 }}>
            {this.state.error}
          </div>
        </div>
      );
    }

    if (loading) {
      return (
        <div className="template-loading" style={{
          padding: '20px',
          border: '2px dashed #3b82f6',
          borderRadius: '8px',
          background: '#eff6ff',
          color: '#1d4ed8',
          textAlign: 'center'
        }}>
          <div style={{ marginBottom: '8px' }}>⏳ Loading Component...</div>
          <div style={{ fontSize: '14px' }}>{template.name} ({template.type})</div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="template-error" style={{
          padding: '20px',
          border: '2px solid #f59e0b',
          borderRadius: '8px',
          background: '#fffbeb',
          color: '#d97706'
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>⚠️ Component Error</div>
          <div>Template: {template.name} ({template.type})</div>
          <div style={{ fontSize: '12px', marginTop: '8px', opacity: 0.8 }}>
            {error}
          </div>
          <button 
            onClick={() => this.loadComponent()}
            style={{
              marginTop: '12px',
              padding: '8px 16px',
              background: '#f59e0b',
              color: '#ffffff',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Retry
          </button>
        </div>
      );
    }

    if (!Component) {
      return (
        <div className="template-no-component" style={{
          padding: '20px',
          border: '2px dashed #6b7280',
          borderRadius: '8px',
          background: '#f9fafb',
          color: '#374151'
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>❓ No Component</div>
          <div>Template: {template.name} ({template.type})</div>
        </div>
      );
    }

    // Render the actual component
    try {
      return (
        <Component
          config={{ ...template.default_config, ...config }}
          isPlaying={isPlaying}
          onComplete={onComplete}
          template={template}
        />
      );
    } catch (renderError) {
      return (
        <div className="template-render-error" style={{
          padding: '20px',
          border: '2px solid #ef4444',
          borderRadius: '8px',
          background: '#fef2f2',
          color: '#dc2626'
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>🚨 Render Error</div>
          <div>Template: {template.name} ({template.type})</div>
          <div style={{ fontSize: '12px', marginTop: '8px', opacity: 0.8 }}>
            {renderError.message}
          </div>
        </div>
      );
    }
  }
}

// Global enhanced template loader instance
export const enhancedTemplateLoader = new EnhancedTemplateLoader();

// Enhanced Template Loader Status Component
export const EnhancedTemplateLoaderStatus = () => {
  const [status, setStatus] = React.useState(null);
  
  React.useEffect(() => {
    const updateStatus = () => {
      setStatus(enhancedTemplateLoader.getStatus());
    };
    
    updateStatus();
    const interval = setInterval(updateStatus, 5000); // Update every 5 seconds
    
    return () => clearInterval(interval);
  }, []);
  
  if (!status) return null;
  
  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      left: '10px',
      background: '#1f2937',
      color: '#ffffff',
      padding: '16px',
      borderRadius: '8px',
      fontSize: '12px',
      maxWidth: '300px',
      zIndex: 9998,
      fontFamily: 'monospace'
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>📋 Enhanced Template Loader</div>
      <div>Total Templates: {status.totalTemplates}</div>
      
      <div style={{ marginTop: '8px' }}>
        <div style={{ fontWeight: 'bold', fontSize: '11px' }}>Status Breakdown:</div>
        {Object.entries(status.statusCounts).map(([statusType, count]) => (
          <div key={statusType} style={{ fontSize: '10px', marginLeft: '8px' }}>
            {statusType}: {count}
          </div>
        ))}
      </div>
      
      {Object.keys(status.loadingStates).length > 0 && (
        <div style={{ marginTop: '8px' }}>
          <div style={{ fontWeight: 'bold', fontSize: '11px' }}>Loading: {Object.keys(status.loadingStates).length}</div>
        </div>
      )}
      
      {Object.keys(status.errorStates).length > 0 && (
        <div style={{ marginTop: '8px' }}>
          <div style={{ fontWeight: 'bold', fontSize: '11px', color: '#ef4444' }}>Errors: {Object.keys(status.errorStates).length}</div>
        </div>
      )}
    </div>
  );
};

export default enhancedTemplateLoader;