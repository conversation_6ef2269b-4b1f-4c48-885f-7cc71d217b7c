import React, { useMemo } from 'react';

const TemplateRenderer = ({ template, config = {}, isPlaying = false, onComplete }) => {
  // Memoize the final config to prevent infinite re-renders
  const finalConfig = useMemo(() => {
    return { ...template.default_config, ...config };
  }, [template.default_config, config]);
  
  // Create context for the template's renderFunction
  const context = useMemo(() => ({
    React,
    isPlaying,
    onComplete,
    utils: {
      createTimer: (callback, delay) => setTimeout(callback, delay)
    }
  }), [isPlaying, onComplete]);

  try {
    // Execute the template's renderFunction
    if (template.renderFunction) {
      // Check if the function expects the old format (config, context) or new format
      const funcStr = template.renderFunction;
      
      if (funcStr.includes('function(config, context)')) {
        // Old format: function(config, context)
        const renderFunc = new Function('config', 'context', `return (${template.renderFunction})(config, context);`);
        return renderFunc(finalConfig, context);
      } else {
        // New format: function(React, config, isPlaying, onComplete, context)
        const renderFunc = new Function('React', 'config', 'isPlaying', 'onComplete', 'context', `return (${template.renderFunction})(React, config, isPlaying, onComplete, context);`);
        return renderFunc(React, finalConfig, isPlaying, onComplete, context);
      }
    }
    
    // Fallback if no renderFunction
    return React.createElement('div', {
      style: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        color: 'white',
        fontSize: '16px'
      }
    }, 'No render function available');
    
  } catch (error) {
    console.error('Error rendering template:', template.name, error);
    return React.createElement('div', {
      style: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        color: '#ff6b6b',
        fontSize: '14px',
        textAlign: 'center',
        padding: '20px'
      }
    }, `Error rendering ${template.name}: ${error.message}`);
  }
};

export default TemplateRenderer;