/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Animation Keyframes */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-3px); }
  75% { transform: translateX(3px); }
}

@keyframes buttonPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes pinBounce {
  0% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.2); }
  100% { transform: translate(-50%, -50%) scale(1); }
}

@keyframes ripple {
  0% { width: 0; height: 0; opacity: 1; }
  100% { width: var(--ripple-max-width, 120px); height: var(--ripple-max-height, 120px); opacity: 0; }
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
  background: #0a0a0b;
}

.App {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0b 0%, #1a1a2e 50%, #16213e 100%);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(10, 10, 11, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
  box-shadow: 0 4px 30px rgba(139, 92, 246, 0.1);
}

.app-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo h1 {
  color: #8b5cf6;
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #8b5cf6, #a855f7, #c084fc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

.logo-subtitle {
  color: #a855f7;
  font-size: 0.8rem;
  font-weight: 500;
  opacity: 0.9;
  padding: 4px 8px;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.nav-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  padding: 10px 18px;
  border-radius: 8px;
  cursor: pointer;
  margin-left: 8px;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.nav-btn:hover::before {
  left: 100%;
}

.nav-btn:hover,
.nav-btn.active {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
  transform: translateY(-1px);
}

/* Enhanced Button Styles */
.btn-primary, .btn-secondary, .btn-play, .btn-stop, .btn-export {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(139, 92, 246, 0.5);
  background: linear-gradient(135deg, #9333ea, #7c3aed);
}

.btn-primary:hover:not(:disabled)::before {
  left: 100%;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
  border: 2px solid rgba(139, 92, 246, 0.5);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.2);
}

.btn-play {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.btn-stop {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.btn-export {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.hero-btn {
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 700;
}

/* Enhanced Hero Section */
.hero-section {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  animation: slowZoom 20s ease-in-out infinite alternate;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(10, 10, 11, 0.85), rgba(139, 92, 246, 0.3), rgba(10, 10, 11, 0.85));
  animation: overlayPulse 8s ease-in-out infinite;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 900px;
  padding: 0 20px;
  animation: heroContentSlideUp 1s ease-out;
}

.hero-title {
  font-size: 4.5rem;
  font-weight: 900;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #ffffff, #8b5cf6, #a855f7, #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 300% 300%;
  animation: gradientShift 4s ease infinite;
  text-shadow: 0 4px 20px rgba(139, 92, 246, 0.3);
  letter-spacing: -2px;
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.7;
  font-weight: 400;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 3rem;
}

.hero-stats {
  display: flex;
  gap: 3rem;
  justify-content: center;
  flex-wrap: wrap;
  opacity: 0.8;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #8b5cf6;
  text-shadow: 0 2px 10px rgba(139, 92, 246, 0.5);
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.7;
  font-weight: 500;
}

/* Category Filter Section */
.category-filter-section {
  padding: 2rem 0;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.category-filters {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.category-filter-btn {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.category-filter-btn:hover,
.category-filter-btn.active {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
  transform: translateY(-2px);
}

/* Main Content */
.main-content {
  padding: 5rem 0;
  min-height: 50vh;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
  color: white;
}

.section-header h2 {
  font-size: 3rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ffffff, #8b5cf6, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
  font-weight: 800;
}

.section-header p {
  font-size: 1.1rem;
  opacity: 0.8;
  max-width: 600px;
  margin: 0 auto;
}

/* Enhanced Templates Grid */
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
  gap: 2.5rem;
}

.template-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.4s ease;
  position: relative;
  backdrop-filter: blur(10px);
}

.template-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 25px 50px rgba(139, 92, 246, 0.3);
  border-color: rgba(139, 92, 246, 0.5);
  background: rgba(255, 255, 255, 0.08);
}

.template-preview {
  height: 220px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(168, 85, 247, 0.1));
}

.template-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.template-category-badge {
  background: rgba(139, 92, 246, 0.2);
  color: #a855f7;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.template-info {
  padding: 2rem;
}

.template-info h3 {
  color: white;
  font-size: 1.4rem;
  margin-bottom: 0.8rem;
  font-weight: 700;
}

.template-info p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  line-height: 1.6;
}

/* Animation Components Enhanced */
.money-counter {
  transition: all 0.3s ease;
}

.money-counter.ease-out {
  animation: counterGlow 0.5s ease;
}

.text-animation {
  position: relative;
}

.text-fade_in { animation: fadeInUp 2s ease forwards; }
.text-slide_up { animation: slideUpBounce 2s ease forwards; }
.text-bounce { animation: bounceIn 2s ease forwards; }
.text-rotate_in { animation: rotateInScale 2s ease forwards; }
.text-wave { animation: waveText 2s ease forwards; }
.text-glitch { animation: glitchEffect 2s ease forwards; }

.logo-reveal {
  transition: all 0.3s ease;
}

.logo-fade_in { animation: logoFadeIn 3s ease forwards; }
.logo-scale_up { animation: logoScaleUp 3s ease forwards; }
.logo-particle_burst { animation: logoParticleBurst 3s ease forwards; }
.logo-slide_reveal { animation: logoSlideReveal 3s ease forwards; }
.logo-rotation { animation: logoRotation 3s ease forwards; }

/* Loading Animations */
.loading-spinner {
  animation: spin 1s linear infinite;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
}

.bounce-dot {
  animation: bounce 1s infinite;
}

.bounce-dot:nth-child(2) { animation-delay: 0.2s; }
.bounce-dot:nth-child(3) { animation-delay: 0.4s; }

.loading-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-wave {
  display: flex;
  align-items: end;
  justify-content: center;
}

.wave-bar {
  animation: wave 1s ease-in-out infinite;
}

.wave-bar:nth-child(2) { animation-delay: 0.1s; }
.wave-bar:nth-child(3) { animation-delay: 0.2s; }
.wave-bar:nth-child(4) { animation-delay: 0.3s; }
.wave-bar:nth-child(5) { animation-delay: 0.4s; }

.loading-orbit {
  position: relative;
  animation: spin 1s linear infinite;
}

.orbit-dot {
  position: absolute;
  top: 0;
  left: 50%;
  transform-origin: 0 50%;
}

/* Enhanced Editor Modal */
.editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  backdrop-filter: blur(5px);
  animation: modalFadeIn 0.3s ease;
}

.editor-content {
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  border-radius: 20px;
  width: 100%;
  max-width: 1600px;
  height: 90vh;
  border: 1px solid rgba(139, 92, 246, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
  animation: modalSlideUp 0.4s ease;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(139, 92, 246, 0.05);
}

.editor-header h2 {
  color: white;
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.template-category {
  background: rgba(139, 92, 246, 0.2);
  color: #a855f7;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  margin-bottom: 1rem;
  display: inline-block;
}

.project-name-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 10px 15px;
  color: white;
  font-size: 1rem;
  max-width: 300px;
  transition: all 0.3s ease;
}

.project-name-input:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2);
}

.modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

.editor-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.editor-preview {
  flex: 2;
  padding: 2rem;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  background: rgba(0, 0, 0, 0.2);
}

.preview-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.export-controls {
  display: flex;
  gap: 0.5rem;
  margin-left: auto;
}

.preview-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  min-height: 400px;
  border: 2px dashed rgba(139, 92, 246, 0.3);
  position: relative;
}

.animation-preview {
  width: 100%;
  max-width: 700px;
}

.editor-controls {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: rgba(139, 92, 246, 0.05);
}

.editor-controls h3 {
  color: white;
  margin-bottom: 0.5rem;
  font-size: 1.4rem;
  font-weight: 700;
}

.template-description {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2rem;
  font-size: 0.9rem;
  line-height: 1.5;
}

.parameters-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.parameter-control {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.parameter-control:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(139, 92, 246, 0.3);
}

.param-label {
  color: white;
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.param-input, .param-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 10px 15px;
  color: white;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.param-input:focus, .param-select:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2);
}

.color-control {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.param-color {
  width: 60px;
  height: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.param-color:hover {
  border-color: #8b5cf6;
}

.color-value {
  color: rgba(255, 255, 255, 0.7);
  font-family: monospace;
  font-size: 0.9rem;
}

.checkbox-control {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  cursor: pointer;
}

.param-checkbox {
  width: 20px;
  height: 20px;
  accent-color: #8b5cf6;
  cursor: pointer;
}

.checkbox-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
}

.range-control {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.param-range {
  flex: 1;
  accent-color: #8b5cf6;
  height: 6px;
}

.range-value {
  color: #8b5cf6;
  font-weight: 700;
  min-width: 60px;
  text-align: right;
  font-size: 0.95rem;
  background: rgba(139, 92, 246, 0.2);
  padding: 4px 10px;
  border-radius: 6px;
}

.editor-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

/* Enhanced Projects Grid */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

.project-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.project-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 15px 35px rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.3);
}

.project-card h3 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.project-card p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.project-actions {
  display: flex;
  gap: 0.8rem;
  margin-top: 1.5rem;
}

/* Enhanced Empty State */
.empty-state {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  padding: 6rem 0;
  max-width: 500px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.5;
}

.empty-state h3 {
  color: white;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.empty-state p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
  line-height: 1.6;
}

/* Loading and Spinner */
.loading {
  text-align: center;
  color: white;
  padding: 6rem 0;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(139, 92, 246, 0.3);
  border-top: 4px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 2rem;
}

/* Enhanced Footer */
.app-footer {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(26, 26, 46, 0.8));
  padding: 4rem 0 2rem;
  border-top: 1px solid rgba(139, 92, 246, 0.2);
  backdrop-filter: blur(20px);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 2rem;
}

.footer-content h3 {
  color: white;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.footer-content p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0;
}

.footer-features {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.feature-icon {
  font-size: 1.2rem;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.9rem;
}

/* Keyframe Animations */
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes slowZoom {
  0% { transform: scale(1); }
  100% { transform: scale(1.05); }
}

@keyframes overlayPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes heroContentSlideUp {
  from { 
    opacity: 0; 
    transform: translateY(50px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalSlideUp {
  from { 
    opacity: 0; 
    transform: translateY(50px) scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0) scale(1); 
  }
}

@keyframes counterGlow {
  0% { text-shadow: 0 0 10px currentColor; }
  50% { text-shadow: 0 0 30px currentColor, 0 0 50px currentColor; }
  100% { text-shadow: 0 0 10px currentColor; }
}

/* Text Animations */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUpBounce {
  0% { opacity: 0; transform: translateY(50px); }
  60% { opacity: 1; transform: translateY(-10px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes bounceIn {
  0% { opacity: 0; transform: scale(0.3); }
  50% { opacity: 1; transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1); }
}

@keyframes rotateInScale {
  0% { opacity: 0; transform: rotate(-90deg) scale(0); }
  100% { opacity: 1; transform: rotate(0deg) scale(1); }
}

@keyframes waveText {
  0%, 100% { transform: translateY(0); }
  25% { transform: translateY(-10px); }
  75% { transform: translateY(10px); }
}

@keyframes glitchEffect {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

/* Logo Animations */
@keyframes logoFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes logoScaleUp {
  0% { transform: scale(0); opacity: 0; }
  50% { transform: scale(1.2); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes logoParticleBurst {
  0% { transform: scale(0); opacity: 0; }
  30% { transform: scale(1.3); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes logoSlideReveal {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes logoRotation {
  0% { transform: rotate(0deg) scale(0); opacity: 0; }
  50% { transform: rotate(180deg) scale(1.2); opacity: 0.8; }
  100% { transform: rotate(360deg) scale(1); opacity: 1; }
}

/* Loading Animations */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-30px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

@keyframes wave {
  0%, 40%, 100% { transform: scaleY(1); }
  20% { transform: scaleY(2); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Responsive Design */
@media (max-width: 1400px) {
  .editor-body {
    flex-direction: column;
  }
  
  .editor-preview {
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-stats {
    gap: 2rem;
  }
  
  .templates-grid {
    grid-template-columns: 1fr;
  }
  
  .editor-content {
    height: 95vh;
    margin: 10px;
  }
  
  .preview-controls {
    flex-direction: column;
  }
  
  .export-controls {
    margin-left: 0;
  }
  
  .parameters-grid {
    gap: 1rem;
  }
  
  .category-filters {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }
  
  .footer-content {
    flex-direction: column;
    text-align: center;
  }
  
  .footer-features {
    justify-content: center;
  }
}