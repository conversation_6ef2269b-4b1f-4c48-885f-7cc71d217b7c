import React from 'react';

const ParameterEditor = ({ template, config, onConfigChange }) => {
  if (!template || !template.editable_params) {
    return null;
  }

  const handleParamChange = (paramName, value) => {
    const newConfig = { ...config, [paramName]: value };
    onConfigChange(newConfig);
  };

  const renderInput = (param) => {
    const currentValue = config[param.name] !== undefined ? config[param.name] : param.default;

    const inputStyle = {
      width: '100%',
      padding: '8px 12px',
      borderRadius: '6px',
      border: '1px solid #374151',
      background: '#1f2937',
      color: 'white',
      fontSize: '14px'
    };

    const labelStyle = {
      display: 'block',
      marginBottom: '6px',
      color: '#e5e7eb',
      fontSize: '14px',
      fontWeight: '500'
    };

    switch (param.type) {
      case 'number':
        return (
          <div key={param.name} style={{ marginBottom: '16px' }}>
            <label style={labelStyle}>{param.label}</label>
            <input
              type="number"
              value={currentValue}
              min={param.min}
              max={param.max}
              step={param.step || 1}
              onChange={(e) => handleParamChange(param.name, parseFloat(e.target.value) || 0)}
              style={inputStyle}
            />
          </div>
        );

      case 'string':
      case 'text':
        return (
          <div key={param.name} style={{ marginBottom: '16px' }}>
            <label style={labelStyle}>{param.label}</label>
            <input
              type="text"
              value={currentValue}
              maxLength={param.max_length}
              onChange={(e) => handleParamChange(param.name, e.target.value)}
              style={inputStyle}
            />
          </div>
        );

      case 'color':
        return (
          <div key={param.name} style={{ marginBottom: '16px' }}>
            <label style={labelStyle}>{param.label}</label>
            <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
              <input
                type="color"
                value={currentValue}
                onChange={(e) => handleParamChange(param.name, e.target.value)}
                style={{
                  width: '50px',
                  height: '38px',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer'
                }}
              />
              <input
                type="text"
                value={currentValue}
                onChange={(e) => handleParamChange(param.name, e.target.value)}
                style={{ ...inputStyle, flex: 1 }}
              />
            </div>
          </div>
        );

      case 'boolean':
        return (
          <div key={param.name} style={{ marginBottom: '16px' }}>
            <label style={{ ...labelStyle, display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={currentValue}
                onChange={(e) => handleParamChange(param.name, e.target.checked)}
                style={{
                  marginRight: '8px',
                  width: '16px',
                  height: '16px',
                  cursor: 'pointer'
                }}
              />
              {param.label}
            </label>
          </div>
        );

      case 'select':
        return (
          <div key={param.name} style={{ marginBottom: '16px' }}>
            <label style={labelStyle}>{param.label}</label>
            <select
              value={currentValue}
              onChange={(e) => handleParamChange(param.name, e.target.value)}
              style={{
                ...inputStyle,
                cursor: 'pointer'
              }}
            >
              {param.options && param.options.map((option) => {
                const optionValue = typeof option === 'string' ? option : option.value;
                const optionLabel = typeof option === 'string' ? option : option.label;
                return (
                  <option key={optionValue} value={optionValue}>
                    {optionLabel}
                  </option>
                );
              })}
            </select>
          </div>
        );

      case 'range':
        return (
          <div key={param.name} style={{ marginBottom: '16px' }}>
            <label style={labelStyle}>
              {param.label}: {currentValue}
            </label>
            <input
              type="range"
              value={currentValue}
              min={param.min}
              max={param.max}
              step={param.step || 1}
              onChange={(e) => handleParamChange(param.name, parseFloat(e.target.value))}
              style={{
                width: '100%',
                height: '6px',
                borderRadius: '3px',
                background: '#374151',
                outline: 'none',
                cursor: 'pointer'
              }}
            />
          </div>
        );

      default:
        return (
          <div key={param.name} style={{ marginBottom: '16px' }}>
            <label style={labelStyle}>{param.label}</label>
            <input
              type="text"
              value={currentValue}
              onChange={(e) => handleParamChange(param.name, e.target.value)}
              style={inputStyle}
            />
          </div>
        );
    }
  };

  return (
    <div style={{
      background: '#111827',
      borderRadius: '8px',
      padding: '20px',
      marginBottom: '20px',
      border: '1px solid #374151'
    }}>
      <h3 style={{
        color: 'white',
        marginBottom: '20px',
        fontSize: '18px',
        fontWeight: '600'
      }}>
        Template Parameters
      </h3>
      {template.editable_params.map(renderInput)}
    </div>
  );
};

export default ParameterEditor;