{"id": "progress-bar", "type": "progress", "name": "Progress Bar", "description": "Animated progress bar with customizable styling", "version": "1.0.0", "category": "utility", "default_config": {"startValue": 0, "endValue": 100, "duration": 3000, "barColor": "#4CAF50", "backgroundColor": "#e0e0e0", "height": 20, "width": 400, "showPercentage": true, "textColor": "#333", "borderRadius": 10, "animationType": "smooth"}, "editable_params": [{"name": "startValue", "label": "Start Value", "type": "number", "min": 0, "max": 100}, {"name": "endValue", "label": "End Value", "type": "number", "min": 0, "max": 100}, {"name": "duration", "label": "Duration (ms)", "type": "range", "min": 500, "max": 10000, "step": 100}, {"name": "barColor", "label": "Bar Color", "type": "color"}, {"name": "backgroundColor", "label": "Background Color", "type": "color"}, {"name": "height", "label": "Height", "type": "range", "min": 10, "max": 50, "step": 2}, {"name": "width", "label": "<PERSON><PERSON><PERSON>", "type": "range", "min": 200, "max": 600, "step": 20}, {"name": "showPercentage", "label": "Show Percentage", "type": "boolean"}, {"name": "textColor", "label": "Text Color", "type": "color"}, {"name": "borderRadius", "label": "Border Radius", "type": "range", "min": 0, "max": 25, "step": 1}], "renderFunction": "function(React, config, isPlaying, onComplete, context) { const { useState, useEffect, useRef } = React; const [currentValue, setCurrentValue] = useState(config.startValue || 0); const intervalRef = useRef(null); useEffect(() => { if (!isPlaying) { setCurrentValue(config.startValue || 0); if (intervalRef.current) { clearInterval(intervalRef.current); intervalRef.current = null; } return; } const startValue = config.startValue || 0; const endValue = config.endValue || 100; const duration = config.duration || 3000; const steps = 60; const increment = (endValue - startValue) / (duration / (1000 / steps)); let current = startValue; intervalRef.current = setInterval(() => { current += increment; if (current >= endValue) { current = endValue; setCurrentValue(current); clearInterval(intervalRef.current); intervalRef.current = null; if (onComplete) onComplete(); } else { setCurrentValue(current); } }, 1000 / steps); return () => { if (intervalRef.current) { clearInterval(intervalRef.current); intervalRef.current = null; } }; }, [isPlaying, config]); const containerStyle = { padding: '40px', background: '#f5f5f5', borderRadius: '12px', textAlign: 'center', fontFamily: 'Arial, sans-serif', minHeight: '120px', display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column' }; const progressContainerStyle = { width: (config.width || 400) + 'px', height: (config.height || 20) + 'px', backgroundColor: config.backgroundColor || '#e0e0e0', borderRadius: (config.borderRadius || 10) + 'px', overflow: 'hidden', position: 'relative', margin: '20px 0' }; const progressBarStyle = { width: currentValue + '%', height: '100%', backgroundColor: config.barColor || '#4CAF50', transition: 'width 0.1s ease', borderRadius: (config.borderRadius || 10) + 'px' }; const textStyle = { color: config.textColor || '#333', fontSize: '18px', fontWeight: 'bold', marginTop: '10px' }; return React.createElement('div', { style: containerStyle }, React.createElement('div', { style: progressContainerStyle }, React.createElement('div', { style: progressBarStyle })), config.showPercentage && React.createElement('div', { style: textStyle }, Math.round(currentValue) + '%')); }"}