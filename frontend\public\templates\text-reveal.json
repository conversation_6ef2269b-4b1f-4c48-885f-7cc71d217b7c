{"name": "Text Reveal", "description": "Animated text reveal with typewriter effect", "version": "1.0.0", "category": "text", "default_config": {"text": "Hello World!", "fontSize": 36, "color": "#2c3e50", "background": "#ecf0f1", "typeSpeed": 100, "showCursor": true, "cursorColor": "#e74c3c", "fontFamily": "monospace", "textAlign": "center", "animationType": "typewriter"}, "editable_params": [{"name": "text", "label": "Text to Reveal", "type": "text", "max_length": 200}, {"name": "fontSize", "label": "Font Size", "type": "range", "min": 16, "max": 72, "step": 2}, {"name": "color", "label": "Text Color", "type": "color"}, {"name": "background", "label": "Background Color", "type": "color"}, {"name": "typeSpeed", "label": "Type Speed (ms)", "type": "range", "min": 50, "max": 500, "step": 10}, {"name": "showCursor", "label": "Show Cursor", "type": "boolean"}, {"name": "cursorColor", "label": "Cursor Color", "type": "color"}, {"name": "fontFamily", "label": "Font Family", "type": "select", "options": [{"value": "monospace", "label": "Monospace"}, {"value": "<PERSON>l, sans-serif", "label": "<PERSON><PERSON>"}, {"value": "Georgia, serif", "label": "Georgia"}, {"value": "'Courier New', monospace", "label": "Courier New"}]}, {"name": "textAlign", "label": "Text Alignment", "type": "select", "options": [{"value": "left", "label": "Left"}, {"value": "center", "label": "Center"}, {"value": "right", "label": "Right"}]}], "id": "text-reveal", "type": "animation", "renderFunction": "function(React, config, isPlaying, onComplete, context) { const { useState, useEffect, useRef } = React; const [displayedText, setDisplayedText] = useState(''); const [showCursor, setShowCursor] = useState(true); const timeoutRef = useRef(null); const cursorIntervalRef = useRef(null); useEffect(() => { if (!isPlaying) { setDisplayedText(''); setShowCursor(true); if (timeoutRef.current) { clearTimeout(timeoutRef.current); timeoutRef.current = null; } if (cursorIntervalRef.current) { clearInterval(cursorIntervalRef.current); cursorIntervalRef.current = null; } return; } const fullText = config.text || 'Hello World!'; const typeSpeed = config.typeSpeed || 100; let currentIndex = 0; const typeText = () => { if (currentIndex < fullText.length) { setDisplayedText(fullText.substring(0, currentIndex + 1)); currentIndex++; timeoutRef.current = setTimeout(typeText, typeSpeed); } else { if (onComplete) onComplete(); } }; typeText(); if (config.showCursor) { cursorIntervalRef.current = setInterval(() => { setShowCursor(prev => !prev); }, 500); } return () => { if (timeoutRef.current) { clearTimeout(timeoutRef.current); timeoutRef.current = null; } if (cursorIntervalRef.current) { clearInterval(cursorIntervalRef.current); cursorIntervalRef.current = null; } }; }, [isPlaying, config]); const containerStyle = { background: config.background || '#ecf0f1', padding: '40px', borderRadius: '12px', textAlign: config.textAlign || 'center', fontFamily: config.fontFamily || 'monospace', minHeight: '120px', display: 'flex', alignItems: 'center', justifyContent: 'center' }; const textStyle = { fontSize: (config.fontSize || 36) + 'px', color: config.color || '#2c3e50', fontWeight: 'normal', lineHeight: '1.4' }; const cursorStyle = { color: config.cursorColor || '#e74c3c', opacity: (config.showCursor && showCursor) ? 1 : 0, transition: 'opacity 0.1s' }; return React.createElement('div', { style: containerStyle }, React.createElement('div', { style: textStyle }, displayedText, config.showCursor && React.createElement('span', { style: cursorStyle }, '|'))); }"}