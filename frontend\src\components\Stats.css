.stats-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.stats-container h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 2.5rem;
  font-weight: 700;
}

.stats-container h2 {
  color: #444;
  margin-bottom: 20px;
  font-size: 1.5rem;
  font-weight: 600;
}

/* Overview Cards */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 500;
}

/* Sections */
.stats-section {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

/* Popular Templates */
.popular-templates {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.popular-template {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #667eea;
  transition: background-color 0.3s ease;
}

.popular-template:hover {
  background: #e9ecef;
}

.template-rank {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  margin-right: 20px;
  min-width: 40px;
}

.template-info {
  flex: 1;
}

.template-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.template-meta {
  color: #666;
  font-size: 0.9rem;
}

.usage-count {
  background: #667eea;
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Category and Type Stats */
.category-stats,
.type-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.category-item,
.type-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.category-item:last-child,
.type-item:last-child {
  border-bottom: none;
}

.category-name,
.type-name {
  min-width: 150px;
  font-weight: 600;
  color: #333;
  text-transform: capitalize;
}

.category-bar,
.type-bar {
  flex: 1;
  height: 20px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.category-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 10px;
  transition: width 0.8s ease;
}

.type-fill {
  height: 100%;
  background: linear-gradient(90deg, #f093fb, #f5576c);
  border-radius: 10px;
  transition: width 0.8s ease;
}

.category-count,
.type-count {
  min-width: 50px;
  text-align: right;
  font-weight: 600;
  color: #667eea;
}

/* Usage Stats */
.usage-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.usage-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #28a745;
}

.usage-rank {
  font-weight: 700;
  color: #28a745;
  margin-right: 15px;
  min-width: 30px;
}

.usage-template {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.usage-count {
  color: #666;
  font-size: 0.9rem;
}

/* Actions */
.stats-actions {
  text-align: center;
  margin-top: 40px;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Loading and Error States */
.loading,
.error {
  text-align: center;
  padding: 50px;
  font-size: 1.2rem;
}

.loading {
  color: #667eea;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 10px;
  margin: 20px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-container {
    padding: 15px;
  }
  
  .stats-container h1 {
    font-size: 2rem;
  }
  
  .stats-overview {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .stat-number {
    font-size: 2.5rem;
  }
  
  .stats-section {
    padding: 20px;
  }
  
  .popular-template {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .template-rank {
    margin-right: 0;
  }
  
  .category-name,
  .type-name {
    min-width: 100px;
  }
  
  .usage-stats {
    grid-template-columns: 1fr;
  }
}