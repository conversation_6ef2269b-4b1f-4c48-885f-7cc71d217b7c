import React, { useState, useEffect } from 'react';
import { animationRegistry } from './AnimationRegistry';

// Template Auto-Discovery and Loading System
class TemplateLoader {
  constructor() {
    this.loadedTemplates = new Map();
    this.componentCache = new Map();
    this.mediaTypeHandlers = new Map();
    this.initializeMediaTypeHandlers();
  }

  // Initialize handlers for different media types
  initializeMediaTypeHandlers() {
    this.mediaTypeHandlers.set('animation', this.handleAnimationTemplate.bind(this));
    this.mediaTypeHandlers.set('background', this.handleBackgroundTemplate.bind(this));
    this.mediaTypeHandlers.set('emoji', this.handleEmojiTemplate.bind(this));
    this.mediaTypeHandlers.set('audio', this.handleAudioTemplate.bind(this));
  }

  // Auto-discover and load templates from backend
  async autoDiscoverTemplates() {
    try {
      const API = `${process.env.REACT_APP_BACKEND_URL}/api`;
      const response = await fetch(`${API}/templates`);
      const templates = await response.json();
      
      console.log(`Auto-discovered ${templates.length} templates`);
      
      // Process each template
      for (const template of templates) {
        await this.processTemplate(template);
      }
      
      return templates;
    } catch (error) {
      console.error('Failed to auto-discover templates:', error);
      return [];
    }
  }

  // Convert config object to editable_params array format
  convertConfigToEditableParams(config) {
    if (!config) return [];
    
    return Object.entries(config).map(([name, paramDef]) => {
      if (typeof paramDef === 'object' && paramDef.type) {
        return {
          name,
          type: paramDef.type,
          label: paramDef.label || name,
          default: paramDef.default,
          min: paramDef.min,
          max: paramDef.max,
          step: paramDef.step,
          options: paramDef.options,
          placeholder: paramDef.placeholder
        };
      }
      return null;
    }).filter(Boolean);
  }

  // Process a single template and create/register components as needed
  async processTemplate(template) {
    const { type, media_type = 'animation', id } = template;
    
    // Convert config to editable_params if needed
    if (template.config && !template.editable_params) {
      template.editable_params = this.convertConfigToEditableParams(template.config);
      
      // Set default_config from preview_config or config defaults
      if (!template.default_config) {
        if (template.preview_config) {
          template.default_config = template.preview_config;
        } else {
          template.default_config = {};
          Object.entries(template.config).forEach(([name, paramDef]) => {
            if (typeof paramDef === 'object' && paramDef.default !== undefined) {
              template.default_config[name] = paramDef.default;
            }
          });
        }
      }
    }
    
    // Check if we already have a component for this type
    if (animationRegistry.has(type, media_type)) {
      this.loadedTemplates.set(id, template);
      return;
    }

    // Try to auto-generate a component based on template configuration
    const component = await this.generateComponent(template);
    if (component) {
      animationRegistry.register(type, component, media_type);
      this.loadedTemplates.set(id, template);
      console.log(`Auto-generated and registered component for ${media_type}:${type}`);
    }
  }

  // Generate a component based on template configuration
  async generateComponent(template) {
    const { type, media_type = 'animation', default_config } = template;
    const handler = this.mediaTypeHandlers.get(media_type);
    
    if (handler) {
      return handler(template);
    }
    
    console.warn(`No handler found for media type: ${media_type}`);
    return null;
  }

  // Handle animation templates
  handleAnimationTemplate(template) {
    const { type, default_config } = template;
    
    // Create a generic animation component based on configuration
    return ({ config, isPlaying, onComplete }) => {
      const mergedConfig = { ...default_config, ...config };
      
      // Generate component based on template type patterns
      if (type.includes('counter')) {
        return this.createCounterComponent(mergedConfig, isPlaying, onComplete);
      } else if (type.includes('chart') || type.includes('graph')) {
        return this.createChartComponent(mergedConfig, isPlaying, onComplete);
      } else if (type.includes('text') || type.includes('typography')) {
        return this.createTextComponent(mergedConfig, isPlaying, onComplete);
      } else if (type.includes('particle') || type.includes('effect')) {
        return this.createParticleComponent(mergedConfig, isPlaying, onComplete);
      } else if (type.includes('progress') || type.includes('bar')) {
        return this.createProgressComponent(mergedConfig, isPlaying, onComplete);
      } else if (type.includes('button') || type.includes('cta')) {
        return this.createButtonComponent(mergedConfig, isPlaying, onComplete);
      } else if (type.includes('notification') || type.includes('alert') || type.includes('banner')) {
        return this.createNotificationComponent(mergedConfig, isPlaying, onComplete);
      } else if (type.includes('map') || type.includes('geo') || type.includes('location')) {
        return this.createMapComponent(mergedConfig, isPlaying, onComplete);
      } else {
        return this.createGenericComponent(mergedConfig, isPlaying, onComplete, template);
      }
    };
  }

  // Handle background templates
  handleBackgroundTemplate(template) {
    return ({ config, isPlaying, onComplete }) => {
      const mergedConfig = { ...template.default_config, ...config };
      
      return (
        <div 
          className="background-template"
          style={{
            width: '100%',
            height: '100%',
            background: mergedConfig.background || mergedConfig.gradient || mergedConfig.pattern,
            backgroundSize: mergedConfig.background_size || 'cover',
            backgroundPosition: mergedConfig.background_position || 'center',
            backgroundRepeat: mergedConfig.background_repeat || 'no-repeat',
            animation: isPlaying ? `${template.type}Animation ${mergedConfig.duration || 3000}ms ${mergedConfig.easing || 'ease-in-out'}` : 'none'
          }}
        >
          {mergedConfig.overlay && (
            <div 
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: mergedConfig.overlay,
                opacity: mergedConfig.overlay_opacity || 0.5
              }}
            />
          )}
        </div>
      );
    };
  }

  // Handle emoji templates
  handleEmojiTemplate(template) {
    return ({ config, isPlaying, onComplete }) => {
      const mergedConfig = { ...template.default_config, ...config };
      
      return (
        <div 
          className="emoji-template"
          style={{
            fontSize: mergedConfig.size || '48px',
            textAlign: 'center',
            animation: isPlaying ? `${template.type}Animation ${mergedConfig.duration || 1000}ms ${mergedConfig.easing || 'ease-in-out'}` : 'none'
          }}
        >
          {mergedConfig.emoji || '😀'}
        </div>
      );
    };
  }

  // Handle audio templates
  handleAudioTemplate(template) {
    return ({ config, isPlaying, onComplete }) => {
      const mergedConfig = { ...template.default_config, ...config };
      
      return (
        <div className="audio-template">
          <audio 
            autoPlay={isPlaying}
            loop={mergedConfig.loop || false}
            volume={mergedConfig.volume || 1.0}
            onEnded={onComplete}
          >
            <source src={mergedConfig.audio_url} type="audio/mpeg" />
            <source src={mergedConfig.audio_url} type="audio/wav" />
            Your browser does not support the audio element.
          </audio>
          <div className="audio-visualizer">
            🎵 {mergedConfig.title || 'Audio Playing'}
          </div>
        </div>
      );
    };
  }

  // Generic component generators for different animation types
  createCounterComponent(config, isPlaying, onComplete) {
    const [count, setCount] = React.useState(config.start_value || 0);
    
    React.useEffect(() => {
      if (isPlaying) {
        const duration = config.duration || 3000;
        const endValue = config.end_value || 100;
        const startValue = config.start_value || 0;
        const increment = (endValue - startValue) / (duration / 50);
        
        const interval = setInterval(() => {
          setCount(prev => {
            const next = prev + increment;
            if (next >= endValue) {
              clearInterval(interval);
              onComplete && onComplete();
              return endValue;
            }
            return next;
          });
        }, 50);
        
        return () => clearInterval(interval);
      }
    }, [isPlaying]);
    
    return (
      <div style={{
        fontSize: config.font_size || '48px',
        color: config.color || '#00ff88',
        background: config.background || '#000',
        padding: '20px',
        textAlign: 'center',
        fontWeight: config.font_weight || 'bold'
      }}>
        {config.currency || ''}{Math.floor(count).toLocaleString()}
      </div>
    );
  }

  createGenericComponent(config, isPlaying, onComplete, template) {
    return (
      <div 
        className={`generic-animation ${template.type}`}
        style={{
          padding: '20px',
          background: config.background || '#f0f0f0',
          color: config.color || '#333',
          textAlign: 'center',
          animation: isPlaying ? `pulse 2s infinite` : 'none'
        }}
      >
        <h3>{template.name}</h3>
        <p>{template.description}</p>
        <div className="config-preview">
          {Object.entries(config).slice(0, 3).map(([key, value]) => (
            <div key={key} style={{ fontSize: '12px', margin: '2px 0' }}>
              {key}: {String(value).substring(0, 20)}
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Additional component generators (simplified versions)
  createChartComponent(config, isPlaying, onComplete) {
    return (
      <div style={{ padding: '20px', background: config.background || '#1a1a2e' }}>
        <div style={{ color: config.text_color || '#fff' }}>📊 Chart Animation</div>
      </div>
    );
  }

  createTextComponent(config, isPlaying, onComplete) {
    return (
      <div style={{
        fontSize: config.font_size || '24px',
        color: config.color || '#333',
        animation: isPlaying ? 'fadeIn 1s ease-in' : 'none'
      }}>
        {config.text || 'Sample Text'}
      </div>
    );
  }

  createParticleComponent(config, isPlaying, onComplete) {
    return (
      <div style={{
        width: '100%',
        height: '200px',
        background: config.background || '#000',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {isPlaying && Array.from({ length: config.particle_count || 20 }).map((_, i) => (
          <div
            key={i}
            style={{
              position: 'absolute',
              width: `${config.particle_size || 4}px`,
              height: `${config.particle_size || 4}px`,
              background: config.particle_color || '#fbbf24',
              borderRadius: '50%',
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `particleFloat ${Math.random() * 3 + 1}s infinite`
            }}
          />
        ))}
      </div>
    );
  }

  createProgressComponent(config, isPlaying, onComplete) {
    const [progress, setProgress] = React.useState(0);
    
    React.useEffect(() => {
      if (isPlaying) {
        const targetProgress = config.progress || 75;
        const duration = config.duration || 2000;
        const increment = targetProgress / (duration / 50);
        
        const interval = setInterval(() => {
          setProgress(prev => {
            const next = prev + increment;
            if (next >= targetProgress) {
              clearInterval(interval);
              onComplete && onComplete();
              return targetProgress;
            }
            return next;
          });
        }, 50);
        
        return () => clearInterval(interval);
      }
    }, [isPlaying]);
    
    return (
      <div style={{ padding: '20px', background: config.background || '#1f2937' }}>
        <div style={{
          width: '100%',
          height: `${config.height || 20}px`,
          background: config.background_color || '#374151',
          borderRadius: `${config.border_radius || 10}px`,
          overflow: 'hidden'
        }}>
          <div style={{
            width: `${progress}%`,
            height: '100%',
            background: config.bar_color || '#8b5cf6',
            transition: 'width 0.1s ease'
          }} />
        </div>
        {config.show_percentage && (
          <div style={{ color: config.text_color || '#fff', marginTop: '8px' }}>
            {Math.floor(progress)}%
          </div>
        )}
      </div>
    );
  }

  createButtonComponent(config, isPlaying, onComplete) {
    return (
      <button style={{
        padding: `${config.padding_y || 12}px ${config.padding_x || 24}px`,
        background: config.background || '#8b5cf6',
        color: config.color || '#fff',
        border: 'none',
        borderRadius: `${config.border_radius || 8}px`,
        fontSize: config.font_size || '16px',
        cursor: 'pointer',
        animation: isPlaying ? 'buttonPulse 1s infinite' : 'none'
      }}>
        {config.text || 'Click Me'}
      </button>
    );
  }

  createNotificationComponent(config, isPlaying, onComplete) {
    return (
      <div style={{
        padding: '16px',
        background: config.background || '#f59e0b',
        color: config.color || '#fff',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        transform: isPlaying ? 'translateY(0)' : 'translateY(-100%)',
        transition: 'transform 0.3s ease-out'
      }}>
        <div style={{ fontWeight: 'bold' }}>{config.title || 'Notification'}</div>
        <div style={{ fontSize: '14px', opacity: 0.9 }}>{config.message || 'This is a notification message'}</div>
      </div>
    );
  }

  createMapComponent(config, isPlaying, onComplete) {
    return (
      <div style={{
        width: '100%',
        height: '200px',
        background: config.background || '#e5e7eb',
        position: 'relative',
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          fontSize: '24px',
          animation: isPlaying ? 'pinBounce 1s ease-out' : 'none'
        }}>
          📍
        </div>
        {config.label && (
          <div style={{
            position: 'absolute',
            bottom: '10px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: 'rgba(0,0,0,0.8)',
            color: '#fff',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px'
          }}>
            {config.label}
          </div>
        )}
      </div>
    );
  }
}

// Global template loader instance
export const templateLoader = new TemplateLoader();

// React hook for using the template loader
export const useTemplateLoader = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadTemplates = async () => {
      try {
        setLoading(true);
        const discoveredTemplates = await templateLoader.autoDiscoverTemplates();
        setTemplates(discoveredTemplates);
        setError(null);
      } catch (err) {
        setError(err.message);
        console.error('Template loading error:', err);
      } finally {
        setLoading(false);
      }
    };

    loadTemplates();
  }, []);

  return { templates, loading, error, reload: () => templateLoader.autoDiscoverTemplates() };
};

export default templateLoader;