{"id": "countdown-timer", "type": "timer", "name": "Countdown Timer", "description": "Digital countdown timer with customizable styling", "version": "1.0.0", "category": "utility", "default_config": {"startTime": 60, "fontSize": 48, "color": "#ff6b6b", "background": "#2c3e50", "showMilliseconds": false, "warningTime": 10, "warningColor": "#ff4757", "completeMessage": "Time's Up!", "autoStart": true}, "editable_params": [{"name": "startTime", "label": "Start Time (seconds)", "type": "number", "min": 1, "max": 3600}, {"name": "fontSize", "label": "Font Size", "type": "range", "min": 24, "max": 96, "step": 4}, {"name": "color", "label": "Text Color", "type": "color"}, {"name": "background", "label": "Background Color", "type": "color"}, {"name": "showMilliseconds", "label": "Show Milliseconds", "type": "boolean"}, {"name": "warningTime", "label": "Warning Time (seconds)", "type": "number", "min": 1, "max": 60}, {"name": "warningColor", "label": "Warning Color", "type": "color"}, {"name": "completeMessage", "label": "Complete Message", "type": "text", "max_length": 50}, {"name": "autoStart", "label": "Auto Start", "type": "boolean"}], "renderFunction": "function(React, config, isPlaying, onComplete, context) { const { useState, useEffect, useRef } = React; const [timeLeft, setTimeLeft] = useState(config.startTime || 60); const [isComplete, setIsComplete] = useState(false); const intervalRef = useRef(null); useEffect(() => { if (!isPlaying || !config.autoStart) { setTimeLeft(config.startTime || 60); setIsComplete(false); if (intervalRef.current) { clearInterval(intervalRef.current); intervalRef.current = null; } return; } intervalRef.current = setInterval(() => { setTimeLeft(prev => { const newTime = prev - 0.1; if (newTime <= 0) { setIsComplete(true); clearInterval(intervalRef.current); intervalRef.current = null; if (onComplete) onComplete(); return 0; } return newTime; }); }, 100); return () => { if (intervalRef.current) { clearInterval(intervalRef.current); intervalRef.current = null; } }; }, [isPlaying, config]); const formatTime = (seconds) => { if (config.showMilliseconds) { const mins = Math.floor(seconds / 60); const secs = Math.floor(seconds % 60); const ms = Math.floor((seconds % 1) * 100); return mins + ':' + secs.toString().padStart(2, '0') + '.' + ms.toString().padStart(2, '0'); } else { const mins = Math.floor(seconds / 60); const secs = Math.floor(seconds % 60); return mins + ':' + secs.toString().padStart(2, '0'); } }; const isWarning = timeLeft <= (config.warningTime || 10) && timeLeft > 0; const currentColor = isWarning ? (config.warningColor || '#ff4757') : (config.color || '#ff6b6b'); const containerStyle = { background: config.background || '#2c3e50', padding: '40px', borderRadius: '12px', textAlign: 'center', fontFamily: 'monospace', minHeight: '120px', display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column' }; const timeStyle = { fontSize: (config.fontSize || 48) + 'px', fontWeight: 'bold', color: currentColor, marginBottom: '10px', textShadow: isWarning ? '0 0 20px ' + currentColor : 'none' }; if (isComplete) { return React.createElement('div', { style: containerStyle }, React.createElement('div', { style: { ...timeStyle, color: config.warningColor || '#ff4757' } }, config.completeMessage || 'Time is Up!')); } return React.createElement('div', { style: containerStyle }, React.createElement('div', { style: timeStyle }, formatTime(timeLeft))); }"}