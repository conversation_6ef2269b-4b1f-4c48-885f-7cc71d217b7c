import React, { useState, useEffect } from 'react';

// Self-contained Money Counter Animation Component
// No dependency on App.js - completely modular

const MoneyCounter = ({ config, isPlaying, onComplete, template, ...props }) => {
  const [count, setCount] = useState(config.start_value || 0);
  const [isAnimating, setIsAnimating] = useState(false);
  
  useEffect(() => {
    if (!isPlaying) {
      setCount(config.start_value || 0);
      setIsAnimating(false);
      return;
    }
    
    setIsAnimating(true);
    
    const duration = config.duration || 3000;
    const endValue = config.end_value || 100;
    const startValue = config.start_value || 0;
    const steps = Math.max(60, Math.abs(endValue - startValue));
    const increment = (endValue - startValue) / (duration / (1000 / 60));
    
    let currentValue = startValue;
    
    const interval = setInterval(() => {
      currentValue += increment;
      
      if ((increment > 0 && currentValue >= endValue) || 
          (increment < 0 && currentValue <= endValue)) {
        setCount(endValue);
        setIsAnimating(false);
        clearInterval(interval);
        onComplete?.();
      } else {
        setCount(currentValue);
      }
    }, 1000 / 60);
    
    return () => {
      clearInterval(interval);
      setIsAnimating(false);
    };
  }, [isPlaying, config]);
  
  const formatValue = (value) => {
    const currency = config.currency || '';
    const decimalPlaces = config.decimal_places || 0;
    const formattedNumber = value.toFixed(decimalPlaces);
    
    // Add thousand separators if enabled
    if (config.use_separators) {
      const parts = formattedNumber.split('.');
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      return currency + parts.join('.');
    }
    
    return currency + formattedNumber;
  };
  
  return (
    <div 
      className="money-counter-animation"
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: config.padding || '40px 20px',
        background: config.background || 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)',
        borderRadius: config.border_radius || '12px',
        boxShadow: config.box_shadow || '0 10px 30px rgba(0,0,0,0.3)',
        color: config.color || '#ffffff',
        fontFamily: config.font_family || 'Arial, sans-serif',
        position: 'relative',
        overflow: 'hidden',
        minHeight: config.min_height || '120px',
        ...props.style
      }}
    >
      {/* Background animation effect */}
      {isAnimating && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)',
          animation: 'pulse 2s ease-in-out infinite',
          pointerEvents: 'none'
        }} />
      )}
      
      {/* Title */}
      {config.title && (
        <div style={{
          fontSize: config.title_size || '16px',
          fontWeight: config.title_weight || '600',
          marginBottom: '12px',
          opacity: 0.9,
          textAlign: 'center',
          color: config.title_color || 'inherit'
        }}>
          {config.title}
        </div>
      )}
      
      {/* Main counter display */}
      <div style={{
        fontSize: config.font_size || '48px',
        fontWeight: config.font_weight || 'bold',
        lineHeight: 1,
        textAlign: 'center',
        position: 'relative',
        zIndex: 1,
        textShadow: config.text_shadow || '0 2px 4px rgba(0,0,0,0.3)',
        color: config.number_color || 'inherit',
        transform: isAnimating ? 'scale(1.05)' : 'scale(1)',
        transition: 'transform 0.3s ease'
      }}>
        {formatValue(count)}
      </div>
      
      {/* Subtitle */}
      {config.subtitle && (
        <div style={{
          fontSize: config.subtitle_size || '14px',
          fontWeight: config.subtitle_weight || '400',
          marginTop: '12px',
          opacity: 0.8,
          textAlign: 'center',
          color: config.subtitle_color || 'inherit'
        }}>
          {config.subtitle}
        </div>
      )}
      
      {/* Progress indicator */}
      {config.show_progress && (
        <div style={{
          width: '80%',
          height: '4px',
          background: 'rgba(255,255,255,0.2)',
          borderRadius: '2px',
          marginTop: '16px',
          overflow: 'hidden'
        }}>
          <div style={{
            width: `${((count - (config.start_value || 0)) / ((config.end_value || 100) - (config.start_value || 0))) * 100}%`,
            height: '100%',
            background: config.progress_color || '#00ff88',
            borderRadius: '2px',
            transition: 'width 0.1s ease'
          }} />
        </div>
      )}
      
      {/* Status indicator */}
      {config.show_status && (
        <div style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          fontSize: '12px',
          opacity: 0.7,
          background: 'rgba(0,0,0,0.2)',
          padding: '4px 8px',
          borderRadius: '4px'
        }}>
          {isAnimating ? '▶️ Counting' : '⏸️ Ready'}
        </div>
      )}
      
      <style jsx>{`
        @keyframes pulse {
          0%, 100% { opacity: 0.5; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.02); }
        }
        
        .money-counter-animation:hover {
          transform: translateY(-2px);
          box-shadow: 0 15px 40px rgba(0,0,0,0.4);
        }
      `}</style>
    </div>
  );
};

// Component metadata for auto-discovery and registration
export const metadata = {
  name: 'MoneyCounter',
  type: 'money_counter',
  category: 'animations',
  description: 'Animated money counter with customizable currency and formatting',
  version: '1.0.0',
  author: 'Modular Animation System',
  tags: ['counter', 'money', 'numbers', 'animation', 'currency'],
  
  // Default configuration
  defaultConfig: {
    start_value: 0,
    end_value: 1000,
    duration: 3000,
    currency: '$',
    decimal_places: 0,
    use_separators: true,
    font_size: '48px',
    font_weight: 'bold',
    font_family: 'Arial, sans-serif',
    color: '#ffffff',
    number_color: '#00ff88',
    background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)',
    border_radius: '12px',
    padding: '40px 20px',
    title: '',
    title_size: '16px',
    title_weight: '600',
    title_color: '#ffffff',
    subtitle: '',
    subtitle_size: '14px',
    subtitle_weight: '400',
    subtitle_color: '#ffffff',
    show_progress: false,
    progress_color: '#00ff88',
    show_status: false,
    text_shadow: '0 2px 4px rgba(0,0,0,0.3)',
    box_shadow: '0 10px 30px rgba(0,0,0,0.3)',
    min_height: '120px'
  },
  
  // Configuration schema for UI generation
  configSchema: {
    // Values
    start_value: {
      type: 'number',
      label: 'Start Value',
      description: 'Starting value for the counter',
      category: 'values'
    },
    end_value: {
      type: 'number',
      label: 'End Value', 
      description: 'Ending value for the counter',
      category: 'values'
    },
    duration: {
      type: 'number',
      label: 'Duration (ms)',
      description: 'Animation duration in milliseconds',
      min: 100,
      max: 30000,
      step: 100,
      category: 'timing'
    },
    
    // Formatting
    currency: {
      type: 'text',
      label: 'Currency Symbol',
      description: 'Currency symbol to display (e.g., $, €, £)',
      category: 'formatting'
    },
    decimal_places: {
      type: 'number',
      label: 'Decimal Places',
      description: 'Number of decimal places to show',
      min: 0,
      max: 4,
      category: 'formatting'
    },
    use_separators: {
      type: 'boolean',
      label: 'Use Thousand Separators',
      description: 'Add commas for thousands (e.g., 1,000)',
      category: 'formatting'
    },
    
    // Typography
    font_size: {
      type: 'text',
      label: 'Font Size',
      description: 'Font size for the counter (e.g., 48px, 3rem)',
      category: 'typography'
    },
    font_weight: {
      type: 'select',
      label: 'Font Weight',
      description: 'Font weight for the counter',
      options: ['normal', 'bold', '100', '200', '300', '400', '500', '600', '700', '800', '900'],
      category: 'typography'
    },
    font_family: {
      type: 'text',
      label: 'Font Family',
      description: 'Font family for the counter',
      category: 'typography'
    },
    
    // Colors
    color: {
      type: 'color',
      label: 'Text Color',
      description: 'General text color',
      category: 'colors'
    },
    number_color: {
      type: 'color',
      label: 'Number Color',
      description: 'Color for the main counter number',
      category: 'colors'
    },
    background: {
      type: 'text',
      label: 'Background',
      description: 'Background color or gradient',
      category: 'colors'
    },
    
    // Layout
    border_radius: {
      type: 'text',
      label: 'Border Radius',
      description: 'Border radius (e.g., 12px)',
      category: 'layout'
    },
    padding: {
      type: 'text',
      label: 'Padding',
      description: 'Internal padding (e.g., 40px 20px)',
      category: 'layout'
    },
    min_height: {
      type: 'text',
      label: 'Minimum Height',
      description: 'Minimum height of the component',
      category: 'layout'
    },
    
    // Content
    title: {
      type: 'text',
      label: 'Title',
      description: 'Optional title above the counter',
      category: 'content'
    },
    title_size: {
      type: 'text',
      label: 'Title Font Size',
      description: 'Font size for the title',
      category: 'content'
    },
    title_color: {
      type: 'color',
      label: 'Title Color',
      description: 'Color for the title text',
      category: 'content'
    },
    subtitle: {
      type: 'text',
      label: 'Subtitle',
      description: 'Optional subtitle below the counter',
      category: 'content'
    },
    subtitle_size: {
      type: 'text',
      label: 'Subtitle Font Size',
      description: 'Font size for the subtitle',
      category: 'content'
    },
    subtitle_color: {
      type: 'color',
      label: 'Subtitle Color',
      description: 'Color for the subtitle text',
      category: 'content'
    },
    
    // Features
    show_progress: {
      type: 'boolean',
      label: 'Show Progress Bar',
      description: 'Display a progress bar below the counter',
      category: 'features'
    },
    progress_color: {
      type: 'color',
      label: 'Progress Bar Color',
      description: 'Color for the progress bar',
      category: 'features'
    },
    show_status: {
      type: 'boolean',
      label: 'Show Status Indicator',
      description: 'Display animation status in corner',
      category: 'features'
    },
    
    // Effects
    text_shadow: {
      type: 'text',
      label: 'Text Shadow',
      description: 'CSS text shadow for the counter',
      category: 'effects'
    },
    box_shadow: {
      type: 'text',
      label: 'Box Shadow',
      description: 'CSS box shadow for the container',
      category: 'effects'
    }
  },
  
  // Preview configuration for template generation
  previewConfig: {
    start_value: 0,
    end_value: 1000,
    duration: 2000,
    currency: '$',
    decimal_places: 0,
    title: 'Revenue',
    subtitle: 'This Month',
    show_progress: true,
    number_color: '#00ff88'
  }
};

// Auto-registration function (called by modular system)
export const register = (registry) => {
  registry.register(
    metadata.type,
    MoneyCounter,
    'animation',
    {
      source: 'modular-component',
      metadata,
      autoDiscovered: true
    }
  );
};

export default MoneyCounter;