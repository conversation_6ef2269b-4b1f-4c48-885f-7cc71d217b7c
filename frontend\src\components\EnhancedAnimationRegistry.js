import React from 'react';

// Enhanced Animation Registry with Auto-Discovery and Intelligent Fallbacks
class EnhancedAnimationRegistry {
  constructor() {
    this.components = new Map();
    this.mediaTypes = new Set(['animation', 'background', 'emoji', 'audio']);
    this.loadedComponents = new Set();
    this.componentFactories = new Map();
    this.typePatterns = new Map();
    this.errorLog = [];
    this.debugMode = process.env.NODE_ENV === 'development';
    
    // Initialize built-in type patterns and factories
    this.initializeBuiltInFactories();
  }

  // Initialize built-in component factories for common patterns
  initializeBuiltInFactories() {
    // Counter-based animations
    this.registerFactory('counter', this.createCounterFactory());
    this.registerFactory('number', this.createCounterFactory());
    this.registerFactory('timer', this.createCounterFactory());
    
    // Chart/Graph animations
    this.registerFactory('chart', this.createChartFactory());
    this.registerFactory('graph', this.createChartFactory());
    this.registerFactory('bar', this.createChartFactory());
    
    // Text animations
    this.registerFactory('text', this.createTextFactory());
    this.registerFactory('typography', this.createTextFactory());
    this.registerFactory('typewriter', this.createTextFactory());
    
    // Effect animations
    this.registerFactory('particle', this.createParticleFactory());
    this.registerFactory('effect', this.createEffectFactory());
    this.registerFactory('burst', this.createParticleFactory());
    
    // UI elements
    this.registerFactory('button', this.createButtonFactory());
    this.registerFactory('progress', this.createProgressFactory());
    this.registerFactory('loading', this.createLoadingFactory());
    
    // 3D and advanced animations
    this.registerFactory('3d', this.create3DFactory());
    this.registerFactory('flip', this.create3DFactory());
    this.registerFactory('card', this.create3DFactory());
    
    // Audio visualizations
    this.registerFactory('audio', this.createAudioFactory());
    this.registerFactory('waveform', this.createAudioFactory());
    this.registerFactory('visualizer', this.createAudioFactory());
  }

  // Register a component factory for a pattern
  registerFactory(pattern, factory) {
    this.componentFactories.set(pattern, factory);
    this.log(`Registered factory for pattern: ${pattern}`);
  }

  // Enhanced registration with auto-discovery
  register(templateType, component, mediaType = 'animation', options = {}) {
    if (!this.mediaTypes.has(mediaType)) {
      this.logError(`Unknown media type: ${mediaType}. Supported types: ${Array.from(this.mediaTypes).join(', ')}`);
      return false;
    }
    
    const key = `${mediaType}:${templateType}`;
    this.components.set(key, {
      component,
      options,
      registeredAt: new Date().toISOString(),
      source: options.source || 'manual'
    });
    
    this.loadedComponents.add(templateType);
    this.log(`Registered ${mediaType} component for type: ${templateType} (source: ${options.source || 'manual'})`);
    return true;
  }

  // Smart component retrieval with auto-generation
  async get(templateType, mediaType = 'animation', template = null) {
    const key = `${mediaType}:${templateType}`;
    
    // Try exact match first
    const exactMatch = this.components.get(key);
    if (exactMatch) {
      this.log(`Found exact match for ${key}`);
      return exactMatch.component;
    }
    
    // Try fuzzy matching
    const fuzzyMatch = this.findFuzzyMatch(templateType, mediaType);
    if (fuzzyMatch) {
      this.log(`Found fuzzy match for ${templateType}: ${fuzzyMatch}`);
      return this.components.get(`${mediaType}:${fuzzyMatch}`).component;
    }
    
    // Try auto-generation based on patterns
    const autoGenerated = await this.autoGenerateComponent(templateType, mediaType, template);
    if (autoGenerated) {
      this.register(templateType, autoGenerated, mediaType, { source: 'auto-generated' });
      return autoGenerated;
    }
    
    // Create intelligent fallback
    const fallback = this.createIntelligentFallback(templateType, mediaType, template);
    this.register(templateType, fallback, mediaType, { source: 'fallback' });
    
    this.logError(`No component found for ${key}, using intelligent fallback`);
    return fallback;
  }

  // Fuzzy matching for similar component names
  findFuzzyMatch(templateType, mediaType) {
    const candidates = [];
    
    for (const [key, componentData] of this.components.entries()) {
      if (!key.startsWith(`${mediaType}:`)) continue;
      
      const registeredType = key.split(':')[1];
      const similarity = this.calculateSimilarity(templateType, registeredType);
      
      if (similarity > 0.7) { // 70% similarity threshold
        candidates.push({ type: registeredType, similarity });
      }
    }
    
    // Return the most similar match
    if (candidates.length > 0) {
      candidates.sort((a, b) => b.similarity - a.similarity);
      return candidates[0].type;
    }
    
    return null;
  }

  // Calculate string similarity (Levenshtein distance based)
  calculateSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const distance = this.levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  }

  levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  // Auto-generate components based on type patterns
  async autoGenerateComponent(templateType, mediaType, template) {
    // Check for pattern matches
    for (const [pattern, factory] of this.componentFactories.entries()) {
      if (templateType.toLowerCase().includes(pattern)) {
        this.log(`Auto-generating component for ${templateType} using ${pattern} factory`);
        return factory(template);
      }
    }
    
    // Check template config for hints
    if (template && template.default_config) {
      const configKeys = Object.keys(template.default_config);
      
      // Counter detection
      if (configKeys.some(key => ['start_value', 'end_value', 'count', 'number'].includes(key))) {
        return this.componentFactories.get('counter')(template);
      }
      
      // Chart detection
      if (configKeys.some(key => ['data', 'bars', 'chart_type', 'values'].includes(key))) {
        return this.componentFactories.get('chart')(template);
      }
      
      // Text detection
      if (configKeys.some(key => ['text', 'content', 'message', 'title'].includes(key))) {
        return this.componentFactories.get('text')(template);
      }
      
      // Particle detection
      if (configKeys.some(key => ['particle_count', 'particles', 'burst'].includes(key))) {
        return this.componentFactories.get('particle')(template);
      }
    }
    
    return null;
  }

  // Create intelligent fallback with debugging info
  createIntelligentFallback(templateType, mediaType, template) {
    return ({ config, isPlaying, onComplete, ...props }) => {
      const mergedConfig = { ...template?.default_config, ...config };
      
      return (
        <div className="intelligent-fallback" style={{
          padding: '20px',
          border: '2px dashed #fbbf24',
          borderRadius: '8px',
          background: 'linear-gradient(135deg, #fef3c7, #fde68a)',
          color: '#92400e',
          fontFamily: 'monospace'
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: '12px', fontSize: '16px' }}>
            🔧 Auto-Generated Fallback
          </div>
          
          <div style={{ marginBottom: '8px' }}>
            <strong>Type:</strong> {templateType} ({mediaType})
          </div>
          
          {template?.name && (
            <div style={{ marginBottom: '8px' }}>
              <strong>Name:</strong> {template.name}
            </div>
          )}
          
          {template?.description && (
            <div style={{ marginBottom: '8px' }}>
              <strong>Description:</strong> {template.description}
            </div>
          )}
          
          <div style={{ marginBottom: '12px' }}>
            <strong>Status:</strong> {isPlaying ? '▶️ Playing' : '⏸️ Paused'}
          </div>
          
          <details style={{ marginBottom: '12px' }}>
            <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>Configuration</summary>
            <pre style={{ 
              background: '#f3f4f6', 
              padding: '8px', 
              borderRadius: '4px', 
              fontSize: '12px',
              overflow: 'auto',
              maxHeight: '150px'
            }}>
              {JSON.stringify(mergedConfig, null, 2)}
            </pre>
          </details>
          
          <div style={{ fontSize: '12px', opacity: 0.8 }}>
            💡 To fix: Create a React component and register it with type "{templateType}"
          </div>
          
          {this.debugMode && (
            <div style={{ marginTop: '12px', fontSize: '11px', opacity: 0.7 }}>
              <strong>Debug Info:</strong>
              <br />• Registered types: {this.getRegisteredTypes().join(', ')}
              <br />• Suggested patterns: {this.getSuggestedPatterns(templateType).join(', ')}
            </div>
          )}
        </div>
      );
    };
  }

  // Get suggested patterns for a template type
  getSuggestedPatterns(templateType) {
    const patterns = [];
    const lowerType = templateType.toLowerCase();
    
    for (const pattern of this.componentFactories.keys()) {
      if (lowerType.includes(pattern)) {
        patterns.push(pattern);
      }
    }
    
    return patterns.length > 0 ? patterns : ['generic'];
  }

  // Component factory implementations
  createCounterFactory() {
    return (template) => {
      return ({ config, isPlaying, onComplete }) => {
        const [count, setCount] = React.useState(config.start_value || 0);
        
        React.useEffect(() => {
          if (!isPlaying) {
            setCount(config.start_value || 0);
            return;
          }
          
          const duration = config.duration || 3000;
          const endValue = config.end_value || 100;
          const startValue = config.start_value || 0;
          const steps = 60;
          const increment = (endValue - startValue) / (duration / (1000 / steps));
          
          const interval = setInterval(() => {
            setCount(prev => {
              const next = prev + increment;
              if ((increment > 0 && next >= endValue) || (increment < 0 && next <= endValue)) {
                clearInterval(interval);
                onComplete?.();
                return endValue;
              }
              return next;
            });
          }, 1000 / steps);
          
          return () => clearInterval(interval);
        }, [isPlaying, config]);
        
        const formatValue = (value) => {
          const currency = config.currency || '';
          const decimalPlaces = config.decimal_places || 0;
          return currency + value.toFixed(decimalPlaces);
        };
        
        return (
          <div style={{
            fontSize: config.font_size || '48px',
            color: config.color || '#00ff88',
            fontWeight: config.font_weight || 'bold',
            background: config.background || 'transparent',
            padding: '20px',
            textAlign: 'center',
            borderRadius: '8px'
          }}>
            {formatValue(count)}
          </div>
        );
      };
    };
  }

  createChartFactory() {
    return (template) => {
      return ({ config, isPlaying, onComplete }) => {
        const [progress, setProgress] = React.useState(0);
        
        React.useEffect(() => {
          if (!isPlaying) {
            setProgress(0);
            return;
          }
          
          const duration = config.duration || 2000;
          const steps = 60;
          const increment = 100 / (duration / (1000 / steps));
          
          const interval = setInterval(() => {
            setProgress(prev => {
              const next = prev + increment;
              if (next >= 100) {
                clearInterval(interval);
                onComplete?.();
                return 100;
              }
              return next;
            });
          }, 1000 / steps);
          
          return () => clearInterval(interval);
        }, [isPlaying, config]);
        
        const data = config.data || [30, 60, 80, 45, 90];
        
        return (
          <div style={{
            padding: '20px',
            background: config.background || '#1a1a2e',
            borderRadius: '8px',
            color: config.text_color || '#fff'
          }}>
            <div style={{ marginBottom: '16px', fontSize: '18px', fontWeight: 'bold' }}>
              📊 {template?.name || 'Chart Animation'}
            </div>
            <div style={{ display: 'flex', alignItems: 'end', gap: '8px', height: '120px' }}>
              {data.map((value, index) => (
                <div
                  key={index}
                  style={{
                    width: '40px',
                    height: `${(value * progress / 100)}%`,
                    background: config.bar_color || '#3b82f6',
                    borderRadius: '4px 4px 0 0',
                    transition: 'height 0.1s ease'
                  }}
                />
              ))}
            </div>
          </div>
        );
      };
    };
  }

  createTextFactory() {
    return (template) => {
      return ({ config, isPlaying, onComplete }) => {
        const [displayText, setDisplayText] = React.useState('');
        const fullText = config.text || config.content || template?.name || 'Text Animation';
        
        React.useEffect(() => {
          if (!isPlaying) {
            setDisplayText('');
            return;
          }
          
          const duration = config.duration || 2000;
          const charDelay = duration / fullText.length;
          
          let currentIndex = 0;
          const interval = setInterval(() => {
            if (currentIndex <= fullText.length) {
              setDisplayText(fullText.slice(0, currentIndex));
              currentIndex++;
            } else {
              clearInterval(interval);
              onComplete?.();
            }
          }, charDelay);
          
          return () => clearInterval(interval);
        }, [isPlaying, config, fullText]);
        
        return (
          <div style={{
            fontSize: config.font_size || '32px',
            color: config.color || '#ffffff',
            fontWeight: config.font_weight || 'normal',
            background: config.background || 'transparent',
            padding: '20px',
            textAlign: config.text_align || 'center',
            borderRadius: '8px',
            fontFamily: config.font_family || 'inherit'
          }}>
            {displayText}
            {isPlaying && displayText.length < fullText.length && (
              <span style={{ opacity: 0.5 }}>|</span>
            )}
          </div>
        );
      };
    };
  }

  createParticleFactory() {
    return (template) => {
      return ({ config, isPlaying, onComplete }) => {
        const [particles, setParticles] = React.useState([]);
        
        React.useEffect(() => {
          if (!isPlaying) {
            setParticles([]);
            return;
          }
          
          const particleCount = config.particle_count || 20;
          const newParticles = Array.from({ length: particleCount }, (_, i) => ({
            id: i,
            x: Math.random() * 100,
            y: Math.random() * 100,
            size: Math.random() * 10 + 5,
            color: config.particle_color || '#fbbf24',
            delay: Math.random() * 1000
          }));
          
          setParticles(newParticles);
          
          const timeout = setTimeout(() => {
            onComplete?.();
          }, config.duration || 3000);
          
          return () => clearTimeout(timeout);
        }, [isPlaying, config]);
        
        return (
          <div style={{
            position: 'relative',
            width: '100%',
            height: '200px',
            background: config.background || 'radial-gradient(circle, #1e1b4b, #0f0f23)',
            borderRadius: '8px',
            overflow: 'hidden'
          }}>
            {particles.map(particle => (
              <div
                key={particle.id}
                style={{
                  position: 'absolute',
                  left: `${particle.x}%`,
                  top: `${particle.y}%`,
                  width: `${particle.size}px`,
                  height: `${particle.size}px`,
                  background: particle.color,
                  borderRadius: '50%',
                  animation: `particleBurst 2s ease-out ${particle.delay}ms forwards`,
                  opacity: 0
                }}
              />
            ))}
            <style jsx>{`
              @keyframes particleBurst {
                0% { opacity: 0; transform: scale(0); }
                50% { opacity: 1; transform: scale(1); }
                100% { opacity: 0; transform: scale(0.5) translateY(-50px); }
              }
            `}</style>
          </div>
        );
      };
    };
  }

  createButtonFactory() {
    return (template) => {
      return ({ config, isPlaying, onComplete }) => {
        const [isHovered, setIsHovered] = React.useState(false);
        
        React.useEffect(() => {
          if (isPlaying) {
            const timeout = setTimeout(() => {
              onComplete?.();
            }, config.duration || 2000);
            return () => clearTimeout(timeout);
          }
        }, [isPlaying, config]);
        
        return (
          <button
            style={{
              padding: '16px 32px',
              fontSize: config.font_size || '18px',
              background: isHovered ? (config.hover_color || '#2563eb') : (config.background || '#3b82f6'),
              color: config.text_color || '#ffffff',
              border: 'none',
              borderRadius: config.border_radius || '8px',
              cursor: 'pointer',
              fontWeight: config.font_weight || 'bold',
              transform: isPlaying ? 'scale(1.05)' : 'scale(1)',
              transition: 'all 0.3s ease',
              boxShadow: isPlaying ? '0 8px 25px rgba(59, 130, 246, 0.5)' : '0 4px 15px rgba(0, 0, 0, 0.2)'
            }}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            {config.text || config.label || template?.name || 'Click Me'}
          </button>
        );
      };
    };
  }

  createProgressFactory() {
    return (template) => {
      return ({ config, isPlaying, onComplete }) => {
        const [progress, setProgress] = React.useState(0);
        
        React.useEffect(() => {
          if (!isPlaying) {
            setProgress(0);
            return;
          }
          
          const duration = config.duration || 3000;
          const targetProgress = config.target_progress || 100;
          const steps = 60;
          const increment = targetProgress / (duration / (1000 / steps));
          
          const interval = setInterval(() => {
            setProgress(prev => {
              const next = prev + increment;
              if (next >= targetProgress) {
                clearInterval(interval);
                onComplete?.();
                return targetProgress;
              }
              return next;
            });
          }, 1000 / steps);
          
          return () => clearInterval(interval);
        }, [isPlaying, config]);
        
        return (
          <div style={{ padding: '20px' }}>
            <div style={{
              width: '100%',
              height: config.height || '20px',
              background: config.track_color || '#e5e7eb',
              borderRadius: config.border_radius || '10px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${progress}%`,
                height: '100%',
                background: config.fill_color || '#3b82f6',
                borderRadius: config.border_radius || '10px',
                transition: 'width 0.1s ease'
              }} />
            </div>
            {config.show_percentage && (
              <div style={{
                textAlign: 'center',
                marginTop: '8px',
                color: config.text_color || '#374151',
                fontSize: config.font_size || '14px'
              }}>
                {Math.round(progress)}%
              </div>
            )}
          </div>
        );
      };
    };
  }

  createLoadingFactory() {
    return (template) => {
      return ({ config, isPlaying, onComplete }) => {
        React.useEffect(() => {
          if (isPlaying) {
            const timeout = setTimeout(() => {
              onComplete?.();
            }, config.duration || 3000);
            return () => clearTimeout(timeout);
          }
        }, [isPlaying, config]);
        
        return (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '40px',
            background: config.background || 'transparent'
          }}>
            <div style={{
              width: config.size || '40px',
              height: config.size || '40px',
              border: `4px solid ${config.track_color || '#e5e7eb'}`,
              borderTop: `4px solid ${config.color || '#3b82f6'}`,
              borderRadius: '50%',
              animation: isPlaying ? 'spin 1s linear infinite' : 'none'
            }} />
            <style jsx>{`
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `}</style>
            {config.text && (
              <div style={{
                marginLeft: '16px',
                color: config.text_color || '#374151',
                fontSize: config.font_size || '16px'
              }}>
                {config.text}
              </div>
            )}
          </div>
        );
      };
    };
  }

  create3DFactory() {
    return (template) => {
      return ({ config, isPlaying, onComplete }) => {
        const [isFlipped, setIsFlipped] = React.useState(false);
        
        React.useEffect(() => {
          if (!isPlaying) {
            setIsFlipped(false);
            return;
          }
          
          const flipInterval = config.flip_interval || 2000;
          const interval = setInterval(() => {
            setIsFlipped(prev => !prev);
          }, flipInterval);
          
          const timeout = setTimeout(() => {
            clearInterval(interval);
            onComplete?.();
          }, config.duration || 6000);
          
          return () => {
            clearInterval(interval);
            clearTimeout(timeout);
          };
        }, [isPlaying, config]);
        
        return (
          <div style={{
            perspective: '1000px',
            width: config.width || '200px',
            height: config.height || '120px',
            margin: '20px auto'
          }}>
            <div style={{
              position: 'relative',
              width: '100%',
              height: '100%',
              transformStyle: 'preserve-3d',
              transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)',
              transition: 'transform 0.8s ease'
            }}>
              {/* Front face */}
              <div style={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                backfaceVisibility: 'hidden',
                background: config.front_color || '#3b82f6',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#ffffff',
                fontSize: config.font_size || '18px',
                fontWeight: 'bold'
              }}>
                {config.front_text || 'Front'}
              </div>
              
              {/* Back face */}
              <div style={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                backfaceVisibility: 'hidden',
                background: config.back_color || '#ef4444',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#ffffff',
                fontSize: config.font_size || '18px',
                fontWeight: 'bold',
                transform: 'rotateY(180deg)'
              }}>
                {config.back_text || 'Back'}
              </div>
            </div>
          </div>
        );
      };
    };
  }

  createAudioFactory() {
    return (template) => {
      return ({ config, isPlaying, onComplete }) => {
        const [bars, setBars] = React.useState([]);
        
        React.useEffect(() => {
          if (!isPlaying) {
            setBars([]);
            return;
          }
          
          const barCount = config.bar_count || 20;
          const updateInterval = config.update_interval || 100;
          
          const interval = setInterval(() => {
            const newBars = Array.from({ length: barCount }, () => Math.random() * 100);
            setBars(newBars);
          }, updateInterval);
          
          const timeout = setTimeout(() => {
            clearInterval(interval);
            onComplete?.();
          }, config.duration || 5000);
          
          return () => {
            clearInterval(interval);
            clearTimeout(timeout);
          };
        }, [isPlaying, config]);
        
        return (
          <div style={{
            padding: '20px',
            background: config.background || 'linear-gradient(135deg, #1e1b4b, #0f0f23)',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'end',
            justifyContent: 'center',
            gap: '2px',
            height: '120px'
          }}>
            {bars.map((height, index) => (
              <div
                key={index}
                style={{
                  width: config.bar_width || '8px',
                  height: `${height}%`,
                  background: config.bar_color || '#00ff88',
                  borderRadius: '2px 2px 0 0',
                  transition: 'height 0.1s ease'
                }}
              />
            ))}
          </div>
        );
      };
    };
  }

  createEffectFactory() {
    return (template) => {
      return ({ config, isPlaying, onComplete }) => {
        const [effects, setEffects] = React.useState([]);
        
        React.useEffect(() => {
          if (!isPlaying) {
            setEffects([]);
            return;
          }
          
          const effectCount = config.effect_count || 10;
          const newEffects = Array.from({ length: effectCount }, (_, i) => ({
            id: i,
            x: Math.random() * 100,
            y: Math.random() * 100,
            scale: Math.random() * 2 + 0.5,
            rotation: Math.random() * 360,
            delay: Math.random() * 2000
          }));
          
          setEffects(newEffects);
          
          const timeout = setTimeout(() => {
            onComplete?.();
          }, config.duration || 4000);
          
          return () => clearTimeout(timeout);
        }, [isPlaying, config]);
        
        return (
          <div style={{
            position: 'relative',
            width: '100%',
            height: '200px',
            background: config.background || 'radial-gradient(circle, #4c1d95, #1e1b4b)',
            borderRadius: '8px',
            overflow: 'hidden'
          }}>
            {effects.map(effect => (
              <div
                key={effect.id}
                style={{
                  position: 'absolute',
                  left: `${effect.x}%`,
                  top: `${effect.y}%`,
                  width: '20px',
                  height: '20px',
                  background: config.effect_color || '#fbbf24',
                  borderRadius: '50%',
                  transform: `scale(${effect.scale}) rotate(${effect.rotation}deg)`,
                  animation: `effectPulse 2s ease-in-out ${effect.delay}ms infinite`,
                  opacity: 0.8
                }}
              />
            ))}
            <style jsx>{`
              @keyframes effectPulse {
                0%, 100% { opacity: 0.3; transform: scale(0.5); }
                50% { opacity: 1; transform: scale(1.2); }
              }
            `}</style>
          </div>
        );
      };
    };
  }

  // Enhanced error logging and debugging
  log(message) {
    if (this.debugMode) {
      console.log(`[EnhancedRegistry] ${message}`);
    }
  }

  logError(message, error = null) {
    const errorEntry = {
      message,
      error: error?.message || null,
      timestamp: new Date().toISOString()
    };
    
    this.errorLog.push(errorEntry);
    console.error(`[EnhancedRegistry] ${message}`, error);
    
    // Keep only last 50 errors
    if (this.errorLog.length > 50) {
      this.errorLog = this.errorLog.slice(-50);
    }
  }

  // Get registry status and diagnostics
  getStatus() {
    return {
      totalComponents: this.components.size,
      registeredTypes: Array.from(this.loadedComponents),
      availableFactories: Array.from(this.componentFactories.keys()),
      recentErrors: this.errorLog.slice(-10),
      mediaTypes: Array.from(this.mediaTypes)
    };
  }

  // Bulk registration from component modules
  async autoRegisterFromModule(modulePromise, mediaType = 'animation') {
    try {
      const module = await modulePromise;
      let registeredCount = 0;
      
      Object.entries(module).forEach(([name, component]) => {
        if (React.isValidElement(component) || typeof component === 'function') {
          const templateType = this.nameToType(name);
          if (this.register(templateType, component, mediaType, { source: 'auto-module' })) {
            registeredCount++;
          }
        }
      });
      
      this.log(`Auto-registered ${registeredCount} components from module`);
      return registeredCount;
    } catch (error) {
      this.logError('Failed to auto-register from module', error);
      return 0;
    }
  }

  // Convert component name to template type
  nameToType(componentName) {
    return componentName
      .replace(/(Component|Animation|Widget)$/, '')
      .replace(/([A-Z])/g, (match, letter, index) => 
        index === 0 ? letter.toLowerCase() : `_${letter.toLowerCase()}`
      );
  }

  // Get all registered template types
  getRegisteredTypes() {
    return Array.from(this.loadedComponents);
  }

  // Check if a component is registered
  has(templateType, mediaType = 'animation') {
    const key = `${mediaType}:${templateType}`;
    return this.components.has(key);
  }

  // Clear all registrations
  clear() {
    this.components.clear();
    this.loadedComponents.clear();
    this.errorLog = [];
    this.log('Registry cleared');
  }
}

// Global enhanced registry instance
export const enhancedAnimationRegistry = new EnhancedAnimationRegistry();

// Enhanced HOC for auto-registering components
export const withEnhancedAutoRegister = (templateType, mediaType = 'animation', options = {}) => {
  return (WrappedComponent) => {
    enhancedAnimationRegistry.register(templateType, WrappedComponent, mediaType, {
      source: 'hoc',
      ...options
    });
    
    return React.forwardRef((props, ref) => (
      <WrappedComponent {...props} ref={ref} />
    ));
  };
};

// Enhanced component factory
export const createEnhancedAnimationComponent = (templateType, renderFunction, mediaType = 'animation', options = {}) => {
  const AnimationComponent = ({ config, isPlaying, onComplete, ...props }) => {
    return renderFunction({ config, isPlaying, onComplete, ...props });
  };
  
  AnimationComponent.displayName = `Enhanced_${templateType}`;
  
  enhancedAnimationRegistry.register(templateType, AnimationComponent, mediaType, {
    source: 'factory',
    ...options
  });
  
  return AnimationComponent;
};

// Registry status component for debugging
export const EnhancedRegistryStatus = () => {
  const [status, setStatus] = React.useState(null);
  
  React.useEffect(() => {
    setStatus(enhancedAnimationRegistry.getStatus());
  }, []);
  
  if (!status) return null;
  
  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: '#1f2937',
      color: '#ffffff',
      padding: '16px',
      borderRadius: '8px',
      fontSize: '12px',
      maxWidth: '300px',
      zIndex: 9999,
      fontFamily: 'monospace'
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>🚀 Enhanced Registry Status</div>
      <div>Components: {status.totalComponents}</div>
      <div>Types: {status.registeredTypes.length}</div>
      <div>Factories: {status.availableFactories.length}</div>
      <div>Errors: {status.recentErrors.length}</div>
      
      {status.recentErrors.length > 0 && (
        <details style={{ marginTop: '8px' }}>
          <summary style={{ cursor: 'pointer' }}>Recent Errors</summary>
          {status.recentErrors.map((error, index) => (
            <div key={index} style={{ fontSize: '10px', opacity: 0.8, marginTop: '4px' }}>
              {error.message}
            </div>
          ))}
        </details>
      )}
    </div>
  );
};

export default enhancedAnimationRegistry;