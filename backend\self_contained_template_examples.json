[{"id": "self_contained_3d_flip_card", "name": "Self-Contained 3D Flip Card", "type": "three_d_flip_card_animation", "category": "ui", "media_type": "animation", "description": "A completely self-contained 3D flip card with embedded rendering logic", "thumbnail": "thumbnails/3d-flip-card.png", "default_config": {"front_text": "HELLO", "back_text": "WORLD", "width": "300px", "height": "200px", "perspective": "1000px", "flip_axis": "Y", "flip_degrees": 180, "duration": 0.8, "easing": "ease-in-out", "flip_delay": 500, "front_bg": "#ffffff", "back_bg": "#111827", "front_text_color": "#000000", "back_text_color": "#ffffff", "border_radius": "12px", "font_size": "24px", "font_weight": "bold", "shadow": true}, "renderFunction": "\n      const [isFlipped, setIsFlipped] = useState(false);\n      const [isAnimating, setIsAnimating] = useState(false);\n      \n      useEffect(() => {\n        if (!isPlaying) {\n          setIsFlipped(false);\n          setIsAnimating(false);\n          return;\n        }\n        \n        setIsAnimating(true);\n        const flipDelay = config.flip_delay || 500;\n        \n        const timer = createTimer(() => {\n          setIsFlipped(true);\n          \n          const duration = (config.duration || 0.8) * 1000;\n          const completeTimer = createTimer(() => {\n            setIsAnimating(false);\n            onComplete?.();\n          }, duration);\n          \n          return () => clearTimer(completeTimer);\n        }, flipDelay);\n        \n        return () => clearTimer(timer);\n      }, [isPlaying]);\n      \n      return React.createElement('div', {\n        style: {\n          width: config.width || '300px',\n          height: config.height || '200px',\n          perspective: config.perspective || '1000px',\n          margin: '20px auto'\n        }\n      }, [\n        React.createElement('div', {\n          key: 'card-inner',\n          style: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            textAlign: 'center',\n            transformStyle: 'preserve-3d',\n            transform: isFlipped ? `rotate${config.flip_axis || 'Y'}(${config.flip_degrees || 180}deg)` : 'rotateY(0deg)',\n            transition: `transform ${config.duration || 0.8}s ${config.easing || 'ease-in-out'}`\n          }\n        }, [\n          // Front face\n          React.createElement('div', {\n            key: 'front',\n            style: {\n              position: 'absolute',\n              width: '100%',\n              height: '100%',\n              backfaceVisibility: 'hidden',\n              background: config.front_bg || '#ffffff',\n              color: config.front_text_color || '#000000',\n              borderRadius: config.border_radius || '12px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: config.font_size || '24px',\n              fontWeight: config.font_weight || 'bold',\n              boxShadow: config.shadow ? '0 8px 25px rgba(0,0,0,0.3)' : 'none',\n              fontFamily: 'Arial, sans-serif'\n            }\n          }, config.front_text || 'FRONT'),\n          \n          // Back face\n          React.createElement('div', {\n            key: 'back',\n            style: {\n              position: 'absolute',\n              width: '100%',\n              height: '100%',\n              backfaceVisibility: 'hidden',\n              background: config.back_bg || '#111827',\n              color: config.back_text_color || '#ffffff',\n              borderRadius: config.border_radius || '12px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: config.font_size || '24px',\n              fontWeight: config.font_weight || 'bold',\n              boxShadow: config.shadow ? '0 8px 25px rgba(0,0,0,0.3)' : 'none',\n              transform: 'rotateY(180deg)',\n              fontFamily: 'Arial, sans-serif'\n            }\n          }, config.back_text || 'BACK')\n        ])\n      ]);\n    ", "editable_params": [{"name": "front_text", "type": "string", "label": "Front Text"}, {"name": "back_text", "type": "string", "label": "Back Text"}, {"name": "width", "type": "text", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "text", "label": "Height"}, {"name": "duration", "type": "number", "label": "Duration (s)", "min": 0.1, "max": 5, "step": 0.1}, {"name": "front_bg", "type": "color", "label": "Front Background"}, {"name": "back_bg", "type": "color", "label": "Back Background"}], "created_at": "2025-01-20T12:00:00.000000"}, {"id": "self_contained_money_counter", "name": "Self-Contained Money Counter", "type": "money_counter_animation", "category": "counters", "media_type": "animation", "description": "A completely self-contained money counter with embedded rendering logic", "thumbnail": "thumbnails/money-counter.png", "default_config": {"start_value": 0, "end_value": 1000, "duration": 2000, "increment": 1, "prefix": "$", "suffix": "", "font_size": "48px", "font_weight": "bold", "color": "#22c55e", "background": "transparent", "padding": "20px", "border_radius": "8px", "font_family": "<PERSON>l, sans-serif", "format_as_currency": true}, "renderFunction": "\n      const [currentValue, setCurrentValue] = useState(config.start_value || 0);\n      const [isAnimating, setIsAnimating] = useState(false);\n      \n      useEffect(() => {\n        if (!isPlaying) {\n          setCurrentValue(config.start_value || 0);\n          setIsAnimating(false);\n          return;\n        }\n        \n        setIsAnimating(true);\n        const startValue = config.start_value || 0;\n        const endValue = config.end_value || 100;\n        const duration = config.duration || 2000;\n        const increment = config.increment || 1;\n        \n        const steps = Math.abs(endValue - startValue) / increment;\n        const stepDuration = duration / steps;\n        \n        let current = startValue;\n        const timer = createInterval(() => {\n          if (startValue < endValue ? current >= endValue : current <= endValue) {\n            setCurrentValue(endValue);\n            clearInterval(timer);\n            setIsAnimating(false);\n            onComplete?.();\n          } else {\n            current += startValue < endValue ? increment : -increment;\n            setCurrentValue(current);\n          }\n        }, stepDuration);\n        \n        return () => clearInterval(timer);\n      }, [isPlaying]);\n      \n      const formatValue = (value) => {\n        if (config.format_as_currency) {\n          return new Intl.NumberFormat('en-US', { \n            style: 'currency', \n            currency: 'USD',\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n          }).format(value);\n        }\n        return value.toLocaleString();\n      };\n      \n      return React.createElement('div', {\n        style: {\n          fontSize: config.font_size || '48px',\n          fontWeight: config.font_weight || 'bold',\n          color: config.color || '#22c55e',\n          background: config.background || 'transparent',\n          padding: config.padding || '20px',\n          borderRadius: config.border_radius || '8px',\n          textAlign: 'center',\n          fontFamily: config.font_family || 'Arial, sans-serif',\n          transition: 'all 0.3s ease',\n          userSelect: 'none'\n        }\n      }, [\n        config.prefix && !config.format_as_currency && React.createElement('span', { key: 'prefix' }, config.prefix),\n        React.createElement('span', { \n          key: 'value',\n          style: {\n            display: 'inline-block',\n            minWidth: '1ch'\n          }\n        }, formatValue(currentValue)),\n        config.suffix && React.createElement('span', { key: 'suffix' }, config.suffix)\n      ]);\n    ", "editable_params": [{"name": "start_value", "type": "number", "label": "Start Value"}, {"name": "end_value", "type": "number", "label": "End Value"}, {"name": "duration", "type": "number", "label": "Duration (ms)", "min": 500, "max": 10000, "step": 100}, {"name": "color", "type": "color", "label": "Text Color"}, {"name": "font_size", "type": "text", "label": "Font Size"}, {"name": "format_as_currency", "type": "boolean", "label": "Format as Currency"}], "created_at": "2025-01-20T12:00:00.000000"}, {"id": "self_contained_audio_waveform", "name": "Self-Contained Audio Waveform", "type": "audio_waveform_animation", "category": "audio", "media_type": "animation", "description": "A completely self-contained audio waveform visualizer with embedded rendering logic", "thumbnail": "thumbnails/audio-waveform.png", "default_config": {"bar_count": 20, "bar_width": 8, "bar_spacing": 4, "max_height": 100, "min_height": 5, "color": "#3b82f6", "background": "transparent", "border_radius": "2px", "animation_speed": 150, "duration": 5000, "gradient": true, "gradient_colors": ["#3b82f6", "#8b5cf6", "#ec4899"]}, "renderFunction": "\n      const [bars, setBars] = useState([]);\n      const [isAnimating, setIsAnimating] = useState(false);\n      \n      useEffect(() => {\n        if (!isPlaying) {\n          setBars([]);\n          setIsAnimating(false);\n          return;\n        }\n        \n        setIsAnimating(true);\n        const barCount = config.bar_count || 20;\n        const animationSpeed = config.animation_speed || 150;\n        const duration = config.duration || 5000;\n        \n        // Initialize bars\n        const initialBars = Array.from({ length: barCount }, () => ({\n          height: config.min_height || 5,\n          opacity: 0.3\n        }));\n        setBars(initialBars);\n        \n        // Animate bars\n        const interval = createInterval(() => {\n          setBars(prevBars => \n            prevBars.map(() => ({\n              height: Math.random() * (config.max_height || 100) + (config.min_height || 5),\n              opacity: 0.7 + Math.random() * 0.3\n            }))\n          );\n        }, animationSpeed);\n        \n        // Complete animation\n        const completeTimer = createTimer(() => {\n          clearInterval(interval);\n          setIsAnimating(false);\n          onComplete?.();\n        }, duration);\n        \n        return () => {\n          clearInterval(interval);\n          clearTimer(completeTimer);\n        };\n      }, [isPlaying]);\n      \n      const getBarColor = (index) => {\n        if (!config.gradient || !config.gradient_colors) {\n          return config.color || '#3b82f6';\n        }\n        \n        const colors = config.gradient_colors;\n        const colorIndex = Math.floor((index / bars.length) * colors.length);\n        return colors[Math.min(colorIndex, colors.length - 1)];\n      };\n      \n      return React.createElement('div', {\n        style: {\n          display: 'flex',\n          alignItems: 'flex-end',\n          justifyContent: 'center',\n          height: (config.max_height || 100) + 20 + 'px',\n          background: config.background || 'transparent',\n          padding: '10px',\n          gap: (config.bar_spacing || 4) + 'px'\n        }\n      }, bars.map((bar, index) => \n        React.createElement('div', {\n          key: index,\n          style: {\n            width: (config.bar_width || 8) + 'px',\n            height: bar.height + 'px',\n            backgroundColor: getBarColor(index),\n            borderRadius: config.border_radius || '2px',\n            opacity: bar.opacity,\n            transition: `height ${config.animation_speed || 150}ms ease-out`\n          }\n        })\n      ));\n    ", "editable_params": [{"name": "bar_count", "type": "number", "label": "Number of Bars", "min": 5, "max": 50, "step": 1}, {"name": "bar_width", "type": "number", "label": "<PERSON> (px)", "min": 2, "max": 20, "step": 1}, {"name": "max_height", "type": "number", "label": "Max Height (px)", "min": 50, "max": 200, "step": 10}, {"name": "color", "type": "color", "label": "Bar Color"}, {"name": "animation_speed", "type": "number", "label": "Animation Speed (ms)", "min": 50, "max": 500, "step": 10}, {"name": "gradient", "type": "boolean", "label": "Use Gradient"}], "created_at": "2025-01-20T12:00:00.000000"}]