{"name": "3D Flip Card", "description": "An interactive 3D flip card animation", "version": "1.0.0", "category": "interactive", "default_config": {"frontText": "Click Me!", "backText": "Hello World!", "cardWidth": 300, "cardHeight": 200, "frontColor": "#4a90e2", "backColor": "#e24a4a", "textColor": "white", "autoFlip": true, "flipDelay": 2000}, "editable_params": [{"name": "frontText", "label": "Front Text", "type": "text", "max_length": 50}, {"name": "backText", "label": "Back Text", "type": "text", "max_length": 50}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON>", "type": "range", "min": 200, "max": 500, "step": 10}, {"name": "cardHeight", "label": "Card Height", "type": "range", "min": 150, "max": 400, "step": 10}, {"name": "frontColor", "label": "Front Color", "type": "color"}, {"name": "backColor", "label": "Back Color", "type": "color"}, {"name": "autoFlip", "label": "Auto Flip", "type": "boolean"}, {"name": "flipDelay", "label": "<PERSON><PERSON> (ms)", "type": "range", "min": 1000, "max": 5000, "step": 500}], "renderFunction": "function(React, config, isPlaying, onComplete, context) { const { useState, useEffect } = React; const { createTimer } = context; const [isFlipped, setIsFlipped] = useState(false); useEffect(() => { if (!isPlaying) { setIsFlipped(false); return; } if (config.autoFlip) { const timer = createTimer(() => { setIsFlipped(true); if (onComplete) { createTimer(() => onComplete(), 500); } }, config.flipDelay || 2000); return () => clearTimeout(timer); } }, [isPlaying, config]); const handleClick = () => { if (!config.autoFlip) { setIsFlipped(!isFlipped); } }; const cardStyle = { width: (config.cardWidth || 300) + 'px', height: (config.cardHeight || 200) + 'px', perspective: '1000px', cursor: config.autoFlip ? 'default' : 'pointer', margin: '20px auto' }; const cardInnerStyle = { position: 'relative', width: '100%', height: '100%', textAlign: 'center', transition: 'transform 0.6s', transformStyle: 'preserve-3d', transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)' }; const cardFaceStyle = { position: 'absolute', width: '100%', height: '100%', backfaceVisibility: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center', borderRadius: '12px', fontSize: '24px', fontWeight: 'bold', color: config.textColor || 'white', boxShadow: '0 4px 8px rgba(0,0,0,0.3)' }; const frontStyle = { ...cardFaceStyle, backgroundColor: config.frontColor || '#4a90e2' }; const backStyle = { ...cardFaceStyle, backgroundColor: config.backColor || '#e24a4a', transform: 'rotateY(180deg)' }; return React.createElement('div', { style: cardStyle, onClick: handleClick }, React.createElement('div', { style: cardInnerStyle }, React.createElement('div', { style: frontStyle }, config.frontText || 'Click Me!'), React.createElement('div', { style: backStyle }, config.backText || 'Hello World!') ) ); }"}