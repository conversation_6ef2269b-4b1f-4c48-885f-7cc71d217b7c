import React, { useState, useEffect } from 'react';
import Template<PERSON><PERSON><PERSON> from './TemplateRenderer';

const TemplateGallery = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [playingTemplate, setPlayingTemplate] = useState(null);

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      
      // Try to load from backend first
      let allTemplates = [];
      try {
        const backendResponse = await fetch('http://localhost:8001/api/templates');
        if (backendResponse.ok) {
          allTemplates = await backendResponse.json();
        }
      } catch (error) {
        console.log('Backend not available, loading frontend templates');
      }
      
      // Load frontend templates
      const frontendTemplates = ['demo', 'flip-card', 'particle-burst', 'countdown-timer', 'progress-bar', 'text-reveal', 'money-counter'];
      
      for (const templateName of frontendTemplates) {
        try {
          const response = await fetch(`/templates/${templateName}.json`);
          if (response.ok) {
            const template = await response.json();
            // Add id field if missing
            if (!template.id) {
              template.id = templateName;
            }
            // Add type field if missing
            if (!template.type) {
              template.type = templateName.replace(/-/g, '_');
            }
            // Avoid duplicates
            if (!allTemplates.find(t => t.id === template.id)) {
              allTemplates.push(template);
            }
          }
        } catch (error) {
          console.log(`Failed to load ${templateName}:`, error);
        }
      }
      
      setTemplates(allTemplates);
    } catch (error) {
      console.error('Error loading templates:', error);
    } finally {
      setLoading(false);
    }
  };

  const categories = ['all', ...new Set(templates.map(t => t.category).filter(Boolean))];
  
  const filteredTemplates = selectedCategory === 'all' 
    ? templates 
    : templates.filter(t => t.category === selectedCategory);

  const handleTemplateClick = (template) => {
    // Navigate to template with autoplay
    const params = new URLSearchParams({
      template: template.id,
      autoplay: 'true'
    });
    window.location.href = `?${params.toString()}`;
  };

  const handlePreview = (template, event) => {
    event.stopPropagation();
    setPlayingTemplate(template.id);
    
    // Stop preview after 3 seconds
    setTimeout(() => {
      setPlayingTemplate(null);
    }, 3000);
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <div style={{ textAlign: 'center', color: 'white' }}>
          <div className="loading-spinner" style={{
            width: '50px',
            height: '50px',
            border: '4px solid rgba(255,255,255,0.3)',
            borderTop: '4px solid white',
            borderRadius: '50%',
            margin: '0 auto 20px'
          }}></div>
          <p>Loading Templates...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '40px 20px'
    }}>
      {/* Header */}
      <div style={{
        textAlign: 'center',
        marginBottom: '50px'
      }}>
        <h1 style={{
          color: 'white',
          fontSize: '3rem',
          fontWeight: '700',
          marginBottom: '1rem',
          textShadow: '0 4px 20px rgba(0,0,0,0.3)'
        }}>
          Motion Stock
        </h1>
        <p style={{
          color: 'rgba(255,255,255,0.9)',
          fontSize: '1.2rem',
          maxWidth: '600px',
          margin: '0 auto'
        }}>
          Professional animation templates for your creative projects
        </p>
      </div>

      {/* Category Filters */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '15px',
        marginBottom: '40px',
        flexWrap: 'wrap'
      }}>
        {categories.map(category => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            className={`category-filter-btn ${selectedCategory === category ? 'active' : ''}`}
            style={{
              padding: '12px 24px',
              borderRadius: '25px',
              border: '2px solid rgba(255,255,255,0.3)',
              background: selectedCategory === category 
                ? 'linear-gradient(135deg, #8b5cf6, #a855f7)' 
                : 'rgba(255,255,255,0.1)',
              color: 'white',
              fontWeight: '600',
              cursor: 'pointer',
              textTransform: 'capitalize',
              transition: 'all 0.3s ease'
            }}
          >
            {category}
          </button>
        ))}
      </div>

      {/* Templates Grid */}
      <div className="templates-grid" style={{
        maxWidth: '1400px',
        margin: '0 auto'
      }}>
        {filteredTemplates.map(template => (
          <div
            key={template.id}
            className="template-card"
            onClick={() => handleTemplateClick(template)}
            style={{ cursor: 'pointer' }}
          >
            {/* Template Preview */}
            <div className="template-preview">
              {playingTemplate === template.id ? (
                <div style={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <TemplateRenderer
                    template={template}
                    config={template.default_config || {}}
                    isPlaying={true}
                    onComplete={() => setPlayingTemplate(null)}
                  />
                </div>
              ) : (
                <div style={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(168, 85, 247, 0.1))',
                  color: 'rgba(255,255,255,0.7)',
                  fontSize: '3rem'
                }}>
                  🎬
                </div>
              )}
              
              {/* Template Overlay */}
              <div className="template-overlay">
                {template.category && (
                  <span className="template-category-badge">
                    {template.category}
                  </span>
                )}
              </div>
              
              {/* Preview Button */}
              <div style={{
                position: 'absolute',
                bottom: '10px',
                right: '10px'
              }}>
                <button
                  onClick={(e) => handlePreview(template, e)}
                  style={{
                    background: 'rgba(139, 92, 246, 0.9)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '20px',
                    padding: '8px 16px',
                    fontSize: '12px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                >
                  Preview
                </button>
              </div>
            </div>
            
            {/* Template Info */}
            <div className="template-info">
              <h3>{template.name}</h3>
              <p>{template.description}</p>
              
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginTop: '15px'
              }}>
                <span style={{
                  color: 'rgba(255,255,255,0.6)',
                  fontSize: '0.85rem'
                }}>
                  {template.type?.replace(/_/g, ' ')}
                </span>
                <button style={{
                  background: 'linear-gradient(135deg, #8b5cf6, #a855f7)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '20px',
                  padding: '8px 16px',
                  fontSize: '12px',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}>
                  Use Template
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {filteredTemplates.length === 0 && (
        <div style={{
          textAlign: 'center',
          color: 'rgba(255,255,255,0.7)',
          marginTop: '50px'
        }}>
          <p>No templates found in this category.</p>
        </div>
      )}
    </div>
  );
};

export default TemplateGallery;