import React from 'react';

// Base Animation Component Interface
export const BaseAnimationComponent = ({ config, isPlaying, onComplete, children }) => {
  return (
    <div className="base-animation-component">
      {children}
    </div>
  );
};

// Animation Component Registry
class AnimationRegistry {
  constructor() {
    this.components = new Map();
    this.mediaTypes = new Set(['animation', 'background', 'emoji', 'audio']);
    this.loadedComponents = new Set();
  }

  // Register a component for a specific template type
  register(templateType, component, mediaType = 'animation') {
    if (!this.mediaTypes.has(mediaType)) {
      console.warn(`Unknown media type: ${mediaType}. Supported types: ${Array.from(this.mediaTypes).join(', ')}`);
    }
    
    const key = `${mediaType}:${templateType}`;
    this.components.set(key, component);
    this.loadedComponents.add(templateType);
    console.log(`Registered ${mediaType} component for type: ${templateType}`);
  }

  // Get component for a template type and media type
  get(templateType, mediaType = 'animation') {
    const key = `${mediaType}:${templateType}`;
    return this.components.get(key);
  }

  // Check if a component is registered
  has(templateType, mediaType = 'animation') {
    const key = `${mediaType}:${templateType}`;
    return this.components.has(key);
  }

  // Get all registered template types
  getRegisteredTypes() {
    return Array.from(this.loadedComponents);
  }

  // Get all components for a media type
  getByMediaType(mediaType) {
    const components = new Map();
    for (const [key, component] of this.components.entries()) {
      if (key.startsWith(`${mediaType}:`)) {
        const templateType = key.split(':')[1];
        components.set(templateType, component);
      }
    }
    return components;
  }

  // Auto-register components from a module
  autoRegister(componentsModule, mediaType = 'animation') {
    Object.entries(componentsModule).forEach(([name, component]) => {
      if (React.isValidElement(component) || typeof component === 'function') {
        // Convert component name to template type (e.g., MoneyCounter -> counter)
        const templateType = this.nameToType(name);
        this.register(templateType, component, mediaType);
      }
    });
  }

  // Convert component name to template type
  nameToType(componentName) {
    // Remove common suffixes and convert to snake_case
    return componentName
      .replace(/(Component|Animation|Widget)$/, '')
      .replace(/([A-Z])/g, (match, letter, index) => 
        index === 0 ? letter.toLowerCase() : `_${letter.toLowerCase()}`
      );
  }

  // Clear all registrations
  clear() {
    this.components.clear();
    this.loadedComponents.clear();
  }
}

// Global registry instance
export const animationRegistry = new AnimationRegistry();

// HOC for auto-registering components
export const withAutoRegister = (templateType, mediaType = 'animation') => {
  return (WrappedComponent) => {
    // Register the component
    animationRegistry.register(templateType, WrappedComponent, mediaType);
    
    // Return the wrapped component
    return React.forwardRef((props, ref) => (
      <WrappedComponent {...props} ref={ref} />
    ));
  };
};

// Component factory for creating animation components
export const createAnimationComponent = (templateType, renderFunction, mediaType = 'animation') => {
  const AnimationComponent = ({ config, isPlaying, onComplete, ...props }) => {
    return renderFunction({ config, isPlaying, onComplete, ...props });
  };
  
  AnimationComponent.displayName = `Animation_${templateType}`;
  
  // Auto-register the component
  animationRegistry.register(templateType, AnimationComponent, mediaType);
  
  return AnimationComponent;
};

export default animationRegistry;