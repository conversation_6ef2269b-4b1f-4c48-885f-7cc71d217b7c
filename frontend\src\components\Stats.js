import React, { useState, useEffect } from 'react';
import './Stats.css';

const Stats = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const response = await fetch(`${process.env.REACT_APP_BACKEND_URL}/api/stats`);
      if (!response.ok) {
        throw new Error('Failed to fetch stats');
      }
      const data = await response.json();
      setStats(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="stats-container">
        <div className="loading">Loading statistics...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="stats-container">
        <div className="error">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="stats-container">
      <h1>📊 Motion Stock Statistics</h1>
      
      {/* Overview Cards */}
      <div className="stats-overview">
        <div className="stat-card">
          <div className="stat-number">{stats.total_templates}</div>
          <div className="stat-label">Total Templates</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{stats.total_projects}</div>
          <div className="stat-label">Total Projects</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{stats.recent_projects_count}</div>
          <div className="stat-label">Recent Projects (7 days)</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{stats.growth_metrics.avg_projects_per_template.toFixed(1)}</div>
          <div className="stat-label">Avg Projects per Template</div>
        </div>
      </div>

      {/* Popular Templates */}
      <div className="stats-section">
        <h2>🔥 Most Popular Templates</h2>
        <div className="popular-templates">
          {stats.popular_templates.map((template, index) => (
            <div key={template.id} className="popular-template">
              <div className="template-rank">#{index + 1}</div>
              <div className="template-info">
                <div className="template-name">{template.name}</div>
                <div className="template-meta">
                  {template.type} • {template.category}
                </div>
              </div>
              <div className="usage-count">{template.usage_count} uses</div>
            </div>
          ))}
        </div>
      </div>

      {/* Category Distribution */}
      <div className="stats-section">
        <h2>📂 Templates by Category</h2>
        <div className="category-stats">
          {stats.template_categories.map((category) => (
            <div key={category.category} className="category-item">
              <div className="category-name">{category.category}</div>
              <div className="category-bar">
                <div 
                  className="category-fill" 
                  style={{ 
                    width: `${(category.count / stats.total_templates) * 100}%` 
                  }}
                ></div>
              </div>
              <div className="category-count">{category.count}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Type Distribution */}
      <div className="stats-section">
        <h2>🎨 Templates by Type</h2>
        <div className="type-stats">
          {stats.template_types.map((type) => (
            <div key={type.type} className="type-item">
              <div className="type-name">{type.type}</div>
              <div className="type-bar">
                <div 
                  className="type-fill" 
                  style={{ 
                    width: `${(type.count / stats.total_templates) * 100}%` 
                  }}
                ></div>
              </div>
              <div className="type-count">{type.count}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Template Usage */}
      <div className="stats-section">
        <h2>📈 Template Usage Overview</h2>
        <div className="usage-stats">
          {stats.template_usage.slice(0, 10).map((usage, index) => (
            <div key={usage.template_id} className="usage-item">
              <div className="usage-rank">#{index + 1}</div>
              <div className="usage-template">Template {usage.template_id}</div>
              <div className="usage-count">{usage.usage_count} projects</div>
            </div>
          ))}
        </div>
      </div>

      {/* Refresh Button */}
      <div className="stats-actions">
        <button onClick={fetchStats} className="refresh-btn">
          🔄 Refresh Statistics
        </button>
      </div>
    </div>
  );
};

export default Stats;