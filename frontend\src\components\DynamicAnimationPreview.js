import React, { useState, useEffect } from 'react';
import { animationRegistry } from './AnimationRegistry';
import { templateLoader } from './TemplateLoader';

// Dynamic Animation Preview Component
const DynamicAnimationPreview = ({ 
  template, 
  config, 
  isPlaying, 
  onComplete, 
  autoPlay = false,
  mediaType = 'animation'
}) => {
  const [localPlaying, setLocalPlaying] = useState(autoPlay);
  const [componentReady, setComponentReady] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (autoPlay) {
      setLocalPlaying(true);
      const timeout = setTimeout(() => {
        setLocalPlaying(false);
        setTimeout(() => setLocalPlaying(true), 1000); // Loop preview
      }, 3000);
      return () => clearTimeout(timeout);
    }
  }, [autoPlay]);

  useEffect(() => {
    // Ensure component is registered for this template type
    const ensureComponent = async () => {
      try {
        if (!animationRegistry.has(template.type, mediaType)) {
          console.log(`Component not found for ${mediaType}:${template.type}, auto-generating...`);
          await templateLoader.processTemplate({ ...template, media_type: mediaType });
        }
        setComponentReady(true);
        setError(null);
      } catch (err) {
        console.error(`Failed to ensure component for ${template.type}:`, err);
        setError(err.message);
      }
    };

    ensureComponent();
  }, [template.type, mediaType]);

  const actualPlaying = isPlaying !== undefined ? isPlaying : localPlaying;

  const renderAnimation = () => {
    if (!componentReady) {
      return (
        <div className="loading-placeholder" style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100px',
          background: '#f3f4f6',
          borderRadius: '8px',
          color: '#6b7280'
        }}>
          Loading {template.name}...
        </div>
      );
    }

    if (error) {
      return (
        <div className="error-placeholder" style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100px',
          background: '#fef2f2',
          borderRadius: '8px',
          color: '#dc2626',
          border: '1px solid #fecaca'
        }}>
          Error loading {template.name}: {error}
        </div>
      );
    }

    // Get the component from registry
    const Component = animationRegistry.get(template.type, mediaType);
    
    if (!Component) {
      return (
        <div className="unknown-type-placeholder" style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100px',
          background: '#fffbeb',
          borderRadius: '8px',
          color: '#d97706',
          border: '1px solid #fed7aa'
        }}>
          <div>Unknown {mediaType} type: {template.type}</div>
          <div style={{ fontSize: '12px', marginTop: '4px' }}>
            Available types: {animationRegistry.getRegisteredTypes().join(', ')}
          </div>
        </div>
      );
    }

    // Render the component with error boundary
    try {
      return (
        <Component 
          config={config} 
          isPlaying={actualPlaying} 
          onComplete={onComplete}
          template={template}
        />
      );
    } catch (renderError) {
      console.error(`Error rendering ${template.type}:`, renderError);
      return (
        <div className="render-error-placeholder" style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100px',
          background: '#fef2f2',
          borderRadius: '8px',
          color: '#dc2626',
          border: '1px solid #fecaca'
        }}>
          Render error: {renderError.message}
        </div>
      );
    }
  };

  return (
    <div className={`dynamic-animation-preview ${mediaType}-preview`}>
      {renderAnimation()}
    </div>
  );
};

// Multi-media preview component that can handle different media types
export const MultiMediaPreview = ({ template, config, isPlaying, onComplete, autoPlay = false }) => {
  const mediaType = template.media_type || 'animation';
  
  return (
    <DynamicAnimationPreview
      template={template}
      config={config}
      isPlaying={isPlaying}
      onComplete={onComplete}
      autoPlay={autoPlay}
      mediaType={mediaType}
    />
  );
};

// Registry status component for debugging
export const RegistryStatus = () => {
  const [registeredTypes, setRegisteredTypes] = useState([]);
  const [mediaTypes, setMediaTypes] = useState({});

  useEffect(() => {
    const updateStatus = () => {
      setRegisteredTypes(animationRegistry.getRegisteredTypes());
      
      const types = {};
      ['animation', 'background', 'emoji', 'audio'].forEach(mediaType => {
        types[mediaType] = Array.from(animationRegistry.getByMediaType(mediaType).keys());
      });
      setMediaTypes(types);
    };

    updateStatus();
    
    // Update every 5 seconds
    const interval = setInterval(updateStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="registry-status" style={{
      position: 'fixed',
      bottom: '10px',
      right: '10px',
      background: 'rgba(0,0,0,0.8)',
      color: '#fff',
      padding: '10px',
      borderRadius: '8px',
      fontSize: '12px',
      maxWidth: '300px',
      zIndex: 1000
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>Registry Status</div>
      <div>Total Types: {registeredTypes.length}</div>
      {Object.entries(mediaTypes).map(([mediaType, types]) => (
        <div key={mediaType} style={{ marginTop: '4px' }}>
          <strong>{mediaType}:</strong> {types.length > 0 ? types.join(', ') : 'none'}
        </div>
      ))}
    </div>
  );
};

// Template validator component
export const TemplateValidator = ({ template }) => {
  const [validation, setValidation] = useState(null);

  useEffect(() => {
    const validateTemplate = () => {
      const errors = [];
      const warnings = [];

      // Required fields
      if (!template.id) errors.push('Missing template ID');
      if (!template.name) errors.push('Missing template name');
      if (!template.type) errors.push('Missing template type');
      if (!template.category) errors.push('Missing template category');

      // Configuration validation
      if (!template.default_config) {
        warnings.push('No default configuration provided');
      } else {
        // Check for common config properties
        const config = template.default_config;
        if (!config.duration && !config.static) {
          warnings.push('No duration specified for animated template');
        }
      }

      // Media type validation
      const validMediaTypes = ['animation', 'background', 'emoji', 'audio'];
      if (template.media_type && !validMediaTypes.includes(template.media_type)) {
        warnings.push(`Unknown media type: ${template.media_type}`);
      }

      setValidation({ errors, warnings, valid: errors.length === 0 });
    };

    validateTemplate();
  }, [template]);

  if (!validation) return null;

  if (validation.valid && validation.warnings.length === 0) {
    return (
      <div style={{ color: '#10b981', fontSize: '12px' }}>
        ✅ Template valid
      </div>
    );
  }

  return (
    <div style={{ fontSize: '12px', marginTop: '8px' }}>
      {validation.errors.map((error, i) => (
        <div key={i} style={{ color: '#dc2626' }}>❌ {error}</div>
      ))}
      {validation.warnings.map((warning, i) => (
        <div key={i} style={{ color: '#d97706' }}>⚠️ {warning}</div>
      ))}
    </div>
  );
};

export default DynamicAnimationPreview;