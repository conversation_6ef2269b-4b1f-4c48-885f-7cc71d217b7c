import React, { useState, useEffect } from 'react';

// Self-contained 3D Flip Card Animation Component
// No dependency on App.js - completely modular

const FlipCard3D = ({ config, isPlaying, onComplete, template, ...props }) => {
  const [isFlipped, setIsFlipped] = useState(false);
  const [flipCount, setFlipCount] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  
  useEffect(() => {
    if (!isPlaying) {
      setIsFlipped(false);
      setFlipCount(0);
      setIsAnimating(false);
      return;
    }
    
    setIsAnimating(true);
    
    const flipInterval = config.flip_interval || 2000;
    const totalFlips = config.total_flips || 3;
    const duration = config.duration || 6000;
    
    let currentFlips = 0;
    
    const flipTimer = setInterval(() => {
      if (currentFlips < totalFlips) {
        setIsFlipped(prev => !prev);
        currentFlips++;
        setFlipCount(currentFlips);
      } else {
        clearInterval(flipTimer);
        setIsAnimating(false);
        onComplete?.();
      }
    }, flipInterval);
    
    // Fallback timeout
    const fallbackTimeout = setTimeout(() => {
      clearInterval(flipTimer);
      setIsAnimating(false);
      onComplete?.();
    }, duration);
    
    return () => {
      clearInterval(flipTimer);
      clearTimeout(fallbackTimeout);
      setIsAnimating(false);
    };
  }, [isPlaying, config]);
  
  const cardStyle = {
    width: config.width || '300px',
    height: config.height || '200px',
    margin: config.margin || '20px auto',
    perspective: config.perspective || '1000px',
    cursor: config.interactive ? 'pointer' : 'default'
  };
  
  const cardInnerStyle = {
    position: 'relative',
    width: '100%',
    height: '100%',
    textAlign: 'center',
    transformStyle: 'preserve-3d',
    transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)',
    transition: `transform ${config.flip_duration || '0.8s'} ${config.flip_easing || 'ease-in-out'}`,
    transformOrigin: config.transform_origin || 'center center'
  };
  
  const faceBaseStyle = {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backfaceVisibility: 'hidden',
    borderRadius: config.border_radius || '12px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: config.padding || '20px',
    boxShadow: config.box_shadow || '0 8px 25px rgba(0,0,0,0.3)',
    border: config.border || 'none',
    fontFamily: config.font_family || 'Arial, sans-serif'
  };
  
  const frontFaceStyle = {
    ...faceBaseStyle,
    background: config.front_background || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: config.front_color || '#ffffff'
  };
  
  const backFaceStyle = {
    ...faceBaseStyle,
    background: config.back_background || 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    color: config.back_color || '#ffffff',
    transform: 'rotateY(180deg)'
  };
  
  const handleCardClick = () => {
    if (config.interactive && !isAnimating) {
      setIsFlipped(prev => !prev);
    }
  };
  
  return (
    <div 
      className="flip-card-3d-animation"
      style={{
        ...cardStyle,
        ...props.style
      }}
      onClick={handleCardClick}
    >
      <div style={cardInnerStyle}>
        {/* Front Face */}
        <div style={frontFaceStyle}>
          {/* Front Icon */}
          {config.front_icon && (
            <div style={{
              fontSize: config.icon_size || '48px',
              marginBottom: '16px',
              opacity: 0.9
            }}>
              {config.front_icon}
            </div>
          )}
          
          {/* Front Title */}
          <div style={{
            fontSize: config.front_title_size || '24px',
            fontWeight: config.front_title_weight || 'bold',
            marginBottom: '12px',
            textAlign: 'center',
            lineHeight: 1.2
          }}>
            {config.front_title || config.front_text || 'Front Side'}
          </div>
          
          {/* Front Subtitle */}
          {config.front_subtitle && (
            <div style={{
              fontSize: config.front_subtitle_size || '16px',
              fontWeight: config.front_subtitle_weight || 'normal',
              opacity: 0.9,
              textAlign: 'center',
              lineHeight: 1.3
            }}>
              {config.front_subtitle}
            </div>
          )}
          
          {/* Front Content */}
          {config.front_content && (
            <div style={{
              fontSize: config.front_content_size || '14px',
              marginTop: '12px',
              opacity: 0.8,
              textAlign: 'center',
              lineHeight: 1.4
            }}>
              {config.front_content}
            </div>
          )}
        </div>
        
        {/* Back Face */}
        <div style={backFaceStyle}>
          {/* Back Icon */}
          {config.back_icon && (
            <div style={{
              fontSize: config.icon_size || '48px',
              marginBottom: '16px',
              opacity: 0.9
            }}>
              {config.back_icon}
            </div>
          )}
          
          {/* Back Title */}
          <div style={{
            fontSize: config.back_title_size || '24px',
            fontWeight: config.back_title_weight || 'bold',
            marginBottom: '12px',
            textAlign: 'center',
            lineHeight: 1.2
          }}>
            {config.back_title || config.back_text || 'Back Side'}
          </div>
          
          {/* Back Subtitle */}
          {config.back_subtitle && (
            <div style={{
              fontSize: config.back_subtitle_size || '16px',
              fontWeight: config.back_subtitle_weight || 'normal',
              opacity: 0.9,
              textAlign: 'center',
              lineHeight: 1.3
            }}>
              {config.back_subtitle}
            </div>
          )}
          
          {/* Back Content */}
          {config.back_content && (
            <div style={{
              fontSize: config.back_content_size || '14px',
              marginTop: '12px',
              opacity: 0.8,
              textAlign: 'center',
              lineHeight: 1.4
            }}>
              {config.back_content}
            </div>
          )}
        </div>
      </div>
      
      {/* Status Indicator */}
      {config.show_status && (
        <div style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          fontSize: '12px',
          background: 'rgba(0,0,0,0.7)',
          color: '#ffffff',
          padding: '4px 8px',
          borderRadius: '4px',
          zIndex: 10
        }}>
          {isAnimating ? `🔄 Flip ${flipCount}/${config.total_flips || 3}` : '⏸️ Ready'}
        </div>
      )}
      
      {/* Interactive Hint */}
      {config.interactive && !isAnimating && (
        <div style={{
          position: 'absolute',
          bottom: '10px',
          left: '50%',
          transform: 'translateX(-50%)',
          fontSize: '11px',
          background: 'rgba(0,0,0,0.7)',
          color: '#ffffff',
          padding: '4px 8px',
          borderRadius: '4px',
          opacity: 0.8
        }}>
          Click to flip
        </div>
      )}
      
      <style jsx>{`
        .flip-card-3d-animation:hover {
          transform: translateY(-5px);
        }
        
        .flip-card-3d-animation {
          transition: transform 0.3s ease;
        }
      `}</style>
    </div>
  );
};

// Component metadata for auto-discovery and registration
export const metadata = {
  name: 'FlipCard3D',
  type: 'three_d_flip_card_animation',
  category: 'animations',
  description: '3D flip card animation with customizable front and back content',
  version: '1.0.0',
  author: 'Modular Animation System',
  tags: ['3d', 'flip', 'card', 'animation', 'transform', 'interactive'],
  
  // Default configuration
  defaultConfig: {
    width: '300px',
    height: '200px',
    margin: '20px auto',
    perspective: '1000px',
    flip_interval: 2000,
    flip_duration: '0.8s',
    flip_easing: 'ease-in-out',
    total_flips: 3,
    duration: 6000,
    border_radius: '12px',
    padding: '20px',
    box_shadow: '0 8px 25px rgba(0,0,0,0.3)',
    border: 'none',
    font_family: 'Arial, sans-serif',
    transform_origin: 'center center',
    interactive: false,
    show_status: false,
    
    // Front face
    front_background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    front_color: '#ffffff',
    front_title: 'Front Side',
    front_title_size: '24px',
    front_title_weight: 'bold',
    front_subtitle: '',
    front_subtitle_size: '16px',
    front_subtitle_weight: 'normal',
    front_content: '',
    front_content_size: '14px',
    front_icon: '',
    
    // Back face
    back_background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    back_color: '#ffffff',
    back_title: 'Back Side',
    back_title_size: '24px',
    back_title_weight: 'bold',
    back_subtitle: '',
    back_subtitle_size: '16px',
    back_subtitle_weight: 'normal',
    back_content: '',
    back_content_size: '14px',
    back_icon: '',
    
    // Icons
    icon_size: '48px'
  },
  
  // Configuration schema for UI generation
  configSchema: {
    // Dimensions
    width: {
      type: 'text',
      label: 'Width',
      description: 'Card width (e.g., 300px, 50%)',
      category: 'dimensions'
    },
    height: {
      type: 'text',
      label: 'Height',
      description: 'Card height (e.g., 200px, 300px)',
      category: 'dimensions'
    },
    margin: {
      type: 'text',
      label: 'Margin',
      description: 'Card margin (e.g., 20px auto)',
      category: 'dimensions'
    },
    
    // Animation
    flip_interval: {
      type: 'number',
      label: 'Flip Interval (ms)',
      description: 'Time between flips in milliseconds',
      min: 500,
      max: 10000,
      step: 100,
      category: 'animation'
    },
    flip_duration: {
      type: 'text',
      label: 'Flip Duration',
      description: 'Duration of each flip (e.g., 0.8s)',
      category: 'animation'
    },
    flip_easing: {
      type: 'select',
      label: 'Flip Easing',
      description: 'Easing function for flip animation',
      options: ['ease', 'ease-in', 'ease-out', 'ease-in-out', 'linear'],
      category: 'animation'
    },
    total_flips: {
      type: 'number',
      label: 'Total Flips',
      description: 'Number of flips during animation',
      min: 1,
      max: 20,
      category: 'animation'
    },
    duration: {
      type: 'number',
      label: 'Total Duration (ms)',
      description: 'Total animation duration',
      min: 1000,
      max: 30000,
      step: 500,
      category: 'animation'
    },
    
    // 3D Settings
    perspective: {
      type: 'text',
      label: 'Perspective',
      description: '3D perspective value (e.g., 1000px)',
      category: '3d_settings'
    },
    transform_origin: {
      type: 'text',
      label: 'Transform Origin',
      description: 'Transform origin for flip (e.g., center center)',
      category: '3d_settings'
    },
    
    // Appearance
    border_radius: {
      type: 'text',
      label: 'Border Radius',
      description: 'Border radius for card corners',
      category: 'appearance'
    },
    padding: {
      type: 'text',
      label: 'Padding',
      description: 'Internal padding for card content',
      category: 'appearance'
    },
    box_shadow: {
      type: 'text',
      label: 'Box Shadow',
      description: 'CSS box shadow for the card',
      category: 'appearance'
    },
    border: {
      type: 'text',
      label: 'Border',
      description: 'CSS border for the card',
      category: 'appearance'
    },
    font_family: {
      type: 'text',
      label: 'Font Family',
      description: 'Font family for card text',
      category: 'appearance'
    },
    
    // Front Face
    front_background: {
      type: 'text',
      label: 'Front Background',
      description: 'Background for front face',
      category: 'front_face'
    },
    front_color: {
      type: 'color',
      label: 'Front Text Color',
      description: 'Text color for front face',
      category: 'front_face'
    },
    front_title: {
      type: 'text',
      label: 'Front Title',
      description: 'Main title for front face',
      category: 'front_face'
    },
    front_title_size: {
      type: 'text',
      label: 'Front Title Size',
      description: 'Font size for front title',
      category: 'front_face'
    },
    front_subtitle: {
      type: 'text',
      label: 'Front Subtitle',
      description: 'Subtitle for front face',
      category: 'front_face'
    },
    front_content: {
      type: 'textarea',
      label: 'Front Content',
      description: 'Additional content for front face',
      category: 'front_face'
    },
    front_icon: {
      type: 'text',
      label: 'Front Icon',
      description: 'Icon/emoji for front face',
      category: 'front_face'
    },
    
    // Back Face
    back_background: {
      type: 'text',
      label: 'Back Background',
      description: 'Background for back face',
      category: 'back_face'
    },
    back_color: {
      type: 'color',
      label: 'Back Text Color',
      description: 'Text color for back face',
      category: 'back_face'
    },
    back_title: {
      type: 'text',
      label: 'Back Title',
      description: 'Main title for back face',
      category: 'back_face'
    },
    back_title_size: {
      type: 'text',
      label: 'Back Title Size',
      description: 'Font size for back title',
      category: 'back_face'
    },
    back_subtitle: {
      type: 'text',
      label: 'Back Subtitle',
      description: 'Subtitle for back face',
      category: 'back_face'
    },
    back_content: {
      type: 'textarea',
      label: 'Back Content',
      description: 'Additional content for back face',
      category: 'back_face'
    },
    back_icon: {
      type: 'text',
      label: 'Back Icon',
      description: 'Icon/emoji for back face',
      category: 'back_face'
    },
    
    // Icons
    icon_size: {
      type: 'text',
      label: 'Icon Size',
      description: 'Size for icons on both faces',
      category: 'icons'
    },
    
    // Features
    interactive: {
      type: 'boolean',
      label: 'Interactive',
      description: 'Allow manual flipping by clicking',
      category: 'features'
    },
    show_status: {
      type: 'boolean',
      label: 'Show Status',
      description: 'Display animation status indicator',
      category: 'features'
    }
  },
  
  // Preview configuration for template generation
  previewConfig: {
    width: '280px',
    height: '180px',
    flip_interval: 1500,
    total_flips: 2,
    duration: 4000,
    front_title: '🎯 Goal',
    front_subtitle: 'Achieve Success',
    front_icon: '🚀',
    back_title: '✅ Complete',
    back_subtitle: 'Mission Accomplished',
    back_icon: '🏆',
    show_status: true
  }
};

// Auto-registration function (called by modular system)
export const register = (registry) => {
  registry.register(
    metadata.type,
    FlipCard3D,
    'animation',
    {
      source: 'modular-component',
      metadata,
      autoDiscovered: true
    }
  );
};

export default FlipCard3D;