import React from 'react';
import { enhancedAnimationRegistry } from './EnhancedAnimationRegistry';

// Modular Animation System - Auto-discovery and Self-contained Components
class ModularAnimationSystem {
  constructor() {
    this.componentModules = new Map();
    this.discoveredComponents = new Set();
    this.loadingPromises = new Map();
    this.debugMode = process.env.NODE_ENV === 'development';
    this.componentPaths = {
      animations: '/src/animations',
      backgrounds: '/src/backgrounds', 
      effects: '/src/effects',
      widgets: '/src/widgets'
    };
    
    // Initialize auto-discovery
    this.initializeAutoDiscovery();
  }

  async initializeAutoDiscovery() {
    try {
      await this.discoverAllComponents();
      this.log('Modular animation system initialized successfully');
    } catch (error) {
      this.logError('Failed to initialize modular animation system', error);
    }
  }

  // Auto-discover all animation components from their respective directories
  async discoverAllComponents() {
    const discoveryPromises = [];
    
    // Discover components from each category
    for (const [category, basePath] of Object.entries(this.componentPaths)) {
      discoveryPromises.push(this.discoverComponentsInCategory(category, basePath));
    }
    
    const results = await Promise.allSettled(discoveryPromises);
    
    let totalDiscovered = 0;
    results.forEach((result, index) => {
      const category = Object.keys(this.componentPaths)[index];
      if (result.status === 'fulfilled') {
        totalDiscovered += result.value;
        this.log(`Discovered ${result.value} components in ${category}`);
      } else {
        this.logError(`Failed to discover components in ${category}`, result.reason);
      }
    });
    
    this.log(`Total components discovered: ${totalDiscovered}`);
    return totalDiscovered;
  }

  // Discover components in a specific category
  async discoverComponentsInCategory(category, basePath) {
    try {
      // In a real implementation, this would scan the filesystem
      // For now, we'll use a convention-based approach with dynamic imports
      const componentFiles = await this.getComponentFilesInPath(basePath);
      
      let discoveredCount = 0;
      
      for (const fileName of componentFiles) {
        try {
          const componentModule = await this.loadComponentModule(basePath, fileName);
          if (componentModule) {
            await this.registerComponentFromModule(componentModule, fileName, category);
            discoveredCount++;
          }
        } catch (error) {
          this.logError(`Failed to load component ${fileName}`, error);
        }
      }
      
      return discoveredCount;
    } catch (error) {
      this.logError(`Failed to discover components in ${basePath}`, error);
      return 0;
    }
  }

  // Get component files in a path (simulated - in real app would use filesystem)
  async getComponentFilesInPath(basePath) {
    // This is a simulation - in a real implementation, you'd scan the actual filesystem
    // or use webpack's require.context() for dynamic imports
    
    const knownComponents = {
      '/src/animations': [
        'MoneyCounter.js',
        'SocialCounter.js', 
        'ParticleBurst.js',
        'TextTypewriter.js',
        'ProgressBar.js',
        'LoadingSpinner.js',
        'FlipCard3D.js',
        'AudioWaveform.js',
        'ChartAnimation.js',
        'ButtonPulse.js'
      ],
      '/src/backgrounds': [
        'GradientBackground.js',
        'ParticleBackground.js',
        'VideoBackground.js'
      ],
      '/src/effects': [
        'GlowEffect.js',
        'BlurEffect.js',
        'ShadowEffect.js'
      ],
      '/src/widgets': [
        'ClockWidget.js',
        'WeatherWidget.js',
        'StatsWidget.js'
      ]
    };
    
    return knownComponents[basePath] || [];
  }

  // Load a component module dynamically
  async loadComponentModule(basePath, fileName) {
    try {
      // Convert path to module path
      const modulePath = `${basePath}/${fileName}`.replace('/src/', '../');
      
      // Use dynamic import (this would work in a real React app)
      // For now, we'll simulate the module structure
      return this.simulateComponentModule(fileName);
    } catch (error) {
      this.logError(`Failed to dynamically import ${modulePath}`, error);
      return null;
    }
  }

  // Simulate component module structure (for demonstration)
  simulateComponentModule(fileName) {
    const componentName = fileName.replace('.js', '');
    
    // Return a simulated module with the expected structure
    return {
      default: this.createSimulatedComponent(componentName),
      metadata: {
        name: componentName,
        type: this.inferTypeFromName(componentName),
        category: this.inferCategoryFromName(componentName),
        description: `Auto-generated ${componentName} component`,
        version: '1.0.0',
        author: 'Auto-discovery System',
        tags: this.generateTagsFromName(componentName),
        defaultConfig: this.generateDefaultConfig(componentName),
        configSchema: this.generateConfigSchema(componentName)
      }
    };
  }

  // Create a simulated component for demonstration
  createSimulatedComponent(componentName) {
    return ({ config, isPlaying, onComplete, ...props }) => {
      const [state, setState] = React.useState({ progress: 0, active: false });
      
      React.useEffect(() => {
        if (!isPlaying) {
          setState({ progress: 0, active: false });
          return;
        }
        
        setState({ progress: 0, active: true });
        
        const duration = config.duration || 3000;
        const steps = 60;
        const increment = 100 / (duration / (1000 / steps));
        
        const interval = setInterval(() => {
          setState(prev => {
            const newProgress = prev.progress + increment;
            if (newProgress >= 100) {
              clearInterval(interval);
              onComplete?.();
              return { ...prev, progress: 100, active: false };
            }
            return { ...prev, progress: newProgress };
          });
        }, 1000 / steps);
        
        return () => clearInterval(interval);
      }, [isPlaying, config]);
      
      return (
        <div style={{
          padding: '20px',
          border: '2px solid #3b82f6',
          borderRadius: '8px',
          background: config.background || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: config.color || '#ffffff',
          textAlign: 'center',
          position: 'relative',
          overflow: 'hidden'
        }}>
          <div style={{ 
            fontSize: config.fontSize || '24px', 
            fontWeight: 'bold',
            marginBottom: '12px'
          }}>
            🎬 {componentName}
          </div>
          
          <div style={{
            fontSize: '14px',
            opacity: 0.9,
            marginBottom: '16px'
          }}>
            Self-contained modular component
          </div>
          
          <div style={{
            width: '100%',
            height: '8px',
            background: 'rgba(255,255,255,0.2)',
            borderRadius: '4px',
            overflow: 'hidden',
            marginBottom: '12px'
          }}>
            <div style={{
              width: `${state.progress}%`,
              height: '100%',
              background: config.progressColor || '#00ff88',
              borderRadius: '4px',
              transition: 'width 0.1s ease'
            }} />
          </div>
          
          <div style={{
            fontSize: '12px',
            opacity: 0.8
          }}>
            Status: {state.active ? '▶️ Playing' : '⏸️ Ready'} | Progress: {Math.round(state.progress)}%
          </div>
          
          {/* Animated background effect */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: state.active ? 
              'radial-gradient(circle at 50% 50%, rgba(255,255,255,0.1) 0%, transparent 70%)' : 
              'none',
            animation: state.active ? 'pulse 2s ease-in-out infinite' : 'none',
            pointerEvents: 'none'
          }} />
          
          <style jsx>{`
            @keyframes pulse {
              0%, 100% { opacity: 0.5; transform: scale(1); }
              50% { opacity: 1; transform: scale(1.05); }
            }
          `}</style>
        </div>
      );
    };
  }

  // Infer component type from name
  inferTypeFromName(componentName) {
    const name = componentName.toLowerCase();
    
    if (name.includes('counter')) return 'counter';
    if (name.includes('chart') || name.includes('graph')) return 'chart';
    if (name.includes('text') || name.includes('typewriter')) return 'text';
    if (name.includes('particle') || name.includes('burst')) return 'particle';
    if (name.includes('progress') || name.includes('bar')) return 'progress';
    if (name.includes('loading') || name.includes('spinner')) return 'loading';
    if (name.includes('3d') || name.includes('flip') || name.includes('card')) return 'three_d_flip_card_animation';
    if (name.includes('audio') || name.includes('waveform') || name.includes('visualizer')) return 'audio_visualizer';
    if (name.includes('button') || name.includes('pulse')) return 'button';
    if (name.includes('background')) return 'background';
    if (name.includes('effect')) return 'effect';
    if (name.includes('widget')) return 'widget';
    
    // Convert camelCase to snake_case
    return componentName
      .replace(/([A-Z])/g, (match, letter, index) => 
        index === 0 ? letter.toLowerCase() : `_${letter.toLowerCase()}`
      );
  }

  // Infer category from name
  inferCategoryFromName(componentName) {
    const name = componentName.toLowerCase();
    
    if (name.includes('background')) return 'backgrounds';
    if (name.includes('effect')) return 'effects';
    if (name.includes('widget')) return 'widgets';
    
    return 'animations';
  }

  // Generate tags from component name
  generateTagsFromName(componentName) {
    const name = componentName.toLowerCase();
    const tags = ['auto-discovered', 'modular'];
    
    if (name.includes('counter')) tags.push('counter', 'numbers');
    if (name.includes('chart')) tags.push('chart', 'data', 'visualization');
    if (name.includes('text')) tags.push('text', 'typography');
    if (name.includes('particle')) tags.push('particle', 'effects');
    if (name.includes('3d')) tags.push('3d', 'transform');
    if (name.includes('audio')) tags.push('audio', 'sound', 'visualization');
    if (name.includes('loading')) tags.push('loading', 'ui');
    if (name.includes('button')) tags.push('button', 'interactive', 'ui');
    
    return tags;
  }

  // Generate default configuration
  generateDefaultConfig(componentName) {
    const name = componentName.toLowerCase();
    const baseConfig = {
      duration: 3000,
      background: 'transparent',
      color: '#ffffff'
    };
    
    if (name.includes('counter')) {
      return {
        ...baseConfig,
        start_value: 0,
        end_value: 100,
        currency: '',
        decimal_places: 0
      };
    }
    
    if (name.includes('chart')) {
      return {
        ...baseConfig,
        data: [30, 60, 80, 45, 90],
        bar_color: '#3b82f6',
        chart_type: 'bar'
      };
    }
    
    if (name.includes('text')) {
      return {
        ...baseConfig,
        text: 'Sample Text',
        font_size: '32px',
        font_weight: 'bold'
      };
    }
    
    if (name.includes('particle')) {
      return {
        ...baseConfig,
        particle_count: 20,
        particle_color: '#fbbf24'
      };
    }
    
    if (name.includes('3d') || name.includes('flip')) {
      return {
        ...baseConfig,
        front_text: 'Front',
        back_text: 'Back',
        front_color: '#3b82f6',
        back_color: '#ef4444'
      };
    }
    
    if (name.includes('audio')) {
      return {
        ...baseConfig,
        bar_count: 20,
        bar_color: '#00ff88',
        update_interval: 100
      };
    }
    
    return baseConfig;
  }

  // Generate configuration schema
  generateConfigSchema(componentName) {
    const name = componentName.toLowerCase();
    const baseSchema = {
      duration: { type: 'number', min: 100, max: 30000, description: 'Animation duration in milliseconds' },
      background: { type: 'color', description: 'Background color or gradient' },
      color: { type: 'color', description: 'Text color' }
    };
    
    if (name.includes('counter')) {
      return {
        ...baseSchema,
        start_value: { type: 'number', description: 'Starting value' },
        end_value: { type: 'number', description: 'Ending value' },
        currency: { type: 'text', description: 'Currency symbol' },
        decimal_places: { type: 'number', min: 0, max: 4, description: 'Number of decimal places' }
      };
    }
    
    if (name.includes('chart')) {
      return {
        ...baseSchema,
        data: { type: 'array', description: 'Chart data values' },
        bar_color: { type: 'color', description: 'Bar color' },
        chart_type: { type: 'select', options: ['bar', 'line', 'pie'], description: 'Chart type' }
      };
    }
    
    return baseSchema;
  }

  // Register component from loaded module
  async registerComponentFromModule(componentModule, fileName, category) {
    try {
      const { default: Component, metadata } = componentModule;
      
      if (!Component || typeof Component !== 'function') {
        throw new Error('Invalid component export');
      }
      
      const componentName = metadata.name || fileName.replace('.js', '');
      const templateType = metadata.type || this.inferTypeFromName(componentName);
      const mediaType = this.getMediaTypeFromCategory(category);
      
      // Register with enhanced registry
      const registered = enhancedAnimationRegistry.register(
        templateType,
        Component,
        mediaType,
        {
          source: 'modular-auto-discovery',
          category,
          metadata,
          fileName,
          discoveredAt: new Date().toISOString()
        }
      );
      
      if (registered) {
        this.componentModules.set(templateType, {
          component: Component,
          metadata,
          fileName,
          category,
          mediaType
        });
        
        this.discoveredComponents.add(templateType);
        this.log(`Registered modular component: ${templateType} from ${fileName}`);
        return true;
      }
      
      return false;
    } catch (error) {
      this.logError(`Failed to register component from ${fileName}`, error);
      return false;
    }
  }

  // Get media type from category
  getMediaTypeFromCategory(category) {
    const mapping = {
      animations: 'animation',
      backgrounds: 'background', 
      effects: 'effect',
      widgets: 'widget'
    };
    
    return mapping[category] || 'animation';
  }

  // Get component with lazy loading
  async getComponent(templateType, mediaType = 'animation') {
    // Check if already loaded
    if (this.componentModules.has(templateType)) {
      return this.componentModules.get(templateType).component;
    }
    
    // Check if loading is in progress
    const loadingKey = `${mediaType}:${templateType}`;
    if (this.loadingPromises.has(loadingKey)) {
      return await this.loadingPromises.get(loadingKey);
    }
    
    // Start loading
    const loadingPromise = this.loadComponentOnDemand(templateType, mediaType);
    this.loadingPromises.set(loadingKey, loadingPromise);
    
    try {
      const component = await loadingPromise;
      this.loadingPromises.delete(loadingKey);
      return component;
    } catch (error) {
      this.loadingPromises.delete(loadingKey);
      throw error;
    }
  }

  // Load component on demand
  async loadComponentOnDemand(templateType, mediaType) {
    try {
      // Try to find and load the component file
      const componentPath = this.findComponentPath(templateType, mediaType);
      
      if (componentPath) {
        const componentModule = await this.loadComponentModule(componentPath.basePath, componentPath.fileName);
        
        if (componentModule) {
          await this.registerComponentFromModule(componentModule, componentPath.fileName, componentPath.category);
          return componentModule.default;
        }
      }
      
      // Fallback to enhanced registry
      return await enhancedAnimationRegistry.get(templateType, mediaType);
    } catch (error) {
      this.logError(`Failed to load component on demand: ${templateType}`, error);
      throw error;
    }
  }

  // Find component path based on type and media type
  findComponentPath(templateType, mediaType) {
    // Convert template type to potential file names
    const potentialNames = this.generatePotentialFileNames(templateType);
    
    // Determine category from media type
    const category = this.getCategoryFromMediaType(mediaType);
    const basePath = this.componentPaths[category];
    
    // For now, return the first potential match
    // In a real implementation, you'd check if the file actually exists
    if (potentialNames.length > 0) {
      return {
        basePath,
        fileName: potentialNames[0],
        category
      };
    }
    
    return null;
  }

  // Generate potential file names from template type
  generatePotentialFileNames(templateType) {
    const names = [];
    
    // Convert snake_case to PascalCase
    const pascalCase = templateType
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('');
    
    names.push(`${pascalCase}.js`);
    
    // Add common variations
    names.push(`${pascalCase}Component.js`);
    names.push(`${pascalCase}Animation.js`);
    
    return names;
  }

  // Get category from media type
  getCategoryFromMediaType(mediaType) {
    const mapping = {
      animation: 'animations',
      background: 'backgrounds',
      effect: 'effects',
      widget: 'widgets'
    };
    
    return mapping[mediaType] || 'animations';
  }

  // Create a new modular component file
  async createComponentFile(templateType, category = 'animations', options = {}) {
    const componentName = this.generateComponentName(templateType);
    const fileName = `${componentName}.js`;
    const basePath = this.componentPaths[category];
    const fullPath = `${basePath}/${fileName}`;
    
    const componentCode = this.generateComponentCode(componentName, templateType, options);
    
    try {
      // In a real implementation, you'd write to the filesystem
      this.log(`Generated component file: ${fullPath}`);
      
      // Simulate the component creation
      const componentModule = {
        default: this.createSimulatedComponent(componentName),
        metadata: {
          name: componentName,
          type: templateType,
          category,
          description: options.description || `${componentName} animation component`,
          version: '1.0.0',
          author: options.author || 'Modular System',
          tags: options.tags || this.generateTagsFromName(componentName),
          defaultConfig: options.defaultConfig || this.generateDefaultConfig(componentName),
          configSchema: options.configSchema || this.generateConfigSchema(componentName)
        }
      };
      
      await this.registerComponentFromModule(componentModule, fileName, category);
      
      return {
        success: true,
        componentName,
        fileName,
        fullPath,
        templateType
      };
    } catch (error) {
      this.logError(`Failed to create component file: ${fullPath}`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Generate component name from template type
  generateComponentName(templateType) {
    return templateType
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('');
  }

  // Generate component code (for file creation)
  generateComponentCode(componentName, templateType, options = {}) {
    return `import React, { useState, useEffect } from 'react';

// Auto-generated ${componentName} component
// Template Type: ${templateType}
// Generated: ${new Date().toISOString()}

const ${componentName} = ({ config, isPlaying, onComplete, template, ...props }) => {
  const [state, setState] = useState({ progress: 0, active: false });
  
  useEffect(() => {
    if (!isPlaying) {
      setState({ progress: 0, active: false });
      return;
    }
    
    setState({ progress: 0, active: true });
    
    const duration = config.duration || 3000;
    const steps = 60;
    const increment = 100 / (duration / (1000 / steps));
    
    const interval = setInterval(() => {
      setState(prev => {
        const newProgress = prev.progress + increment;
        if (newProgress >= 100) {
          clearInterval(interval);
          onComplete?.();
          return { ...prev, progress: 100, active: false };
        }
        return { ...prev, progress: newProgress };
      });
    }, 1000 / steps);
    
    return () => clearInterval(interval);
  }, [isPlaying, config]);
  
  return (
    <div className="${templateType.replace(/_/g, '-')}-animation" style={{
      padding: config.padding || '20px',
      background: config.background || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: config.color || '#ffffff',
      borderRadius: config.borderRadius || '8px',
      textAlign: 'center',
      position: 'relative',
      overflow: 'hidden'
    }}>
      <div style={{
        fontSize: config.fontSize || '24px',
        fontWeight: config.fontWeight || 'bold',
        marginBottom: '12px'
      }}>
        {config.title || '${componentName}'}
      </div>
      
      <div style={{
        fontSize: '14px',
        opacity: 0.9,
        marginBottom: '16px'
      }}>
        {config.subtitle || 'Modular Animation Component'}
      </div>
      
      {/* Progress indicator */}
      <div style={{
        width: '100%',
        height: '8px',
        background: 'rgba(255,255,255,0.2)',
        borderRadius: '4px',
        overflow: 'hidden',
        marginBottom: '12px'
      }}>
        <div style={{
          width: \`\${state.progress}%\`,
          height: '100%',
          background: config.progressColor || '#00ff88',
          borderRadius: '4px',
          transition: 'width 0.1s ease'
        }} />
      </div>
      
      <div style={{
        fontSize: '12px',
        opacity: 0.8
      }}>
        Status: {state.active ? '▶️ Playing' : '⏸️ Ready'} | Progress: {Math.round(state.progress)}%
      </div>
    </div>
  );
};

// Component metadata for auto-discovery
export const metadata = {
  name: '${componentName}',
  type: '${templateType}',
  category: '${options.category || 'animations'}',
  description: '${options.description || `${componentName} animation component`}',
  version: '1.0.0',
  author: '${options.author || 'Modular System'}',
  tags: ${JSON.stringify(options.tags || this.generateTagsFromName(componentName))},
  defaultConfig: ${JSON.stringify(options.defaultConfig || this.generateDefaultConfig(componentName), null, 2)},
  configSchema: ${JSON.stringify(options.configSchema || this.generateConfigSchema(componentName), null, 2)}
};

export default ${componentName};
`;
  }

  // Get system status
  getStatus() {
    return {
      discoveredComponents: this.discoveredComponents.size,
      componentModules: this.componentModules.size,
      loadingPromises: this.loadingPromises.size,
      componentPaths: this.componentPaths,
      discoveredTypes: Array.from(this.discoveredComponents)
    };
  }

  // Utility methods
  log(message) {
    if (this.debugMode) {
      console.log(`[ModularAnimationSystem] ${message}`);
    }
  }

  logError(message, error = null) {
    console.error(`[ModularAnimationSystem] ${message}`, error);
  }
}

// Global modular animation system instance
export const modularAnimationSystem = new ModularAnimationSystem();

// HOC for creating modular components
export const withModularRegistration = (templateType, category = 'animations', options = {}) => {
  return (WrappedComponent) => {
    // Auto-register the component
    const mediaType = modularAnimationSystem.getMediaTypeFromCategory(category);
    
    enhancedAnimationRegistry.register(
      templateType,
      WrappedComponent,
      mediaType,
      {
        source: 'modular-hoc',
        category,
        ...options
      }
    );
    
    return React.forwardRef((props, ref) => (
      <WrappedComponent {...props} ref={ref} />
    ));
  };
};

// Component factory for quick creation
export const createModularComponent = (templateType, renderFunction, options = {}) => {
  const componentName = modularAnimationSystem.generateComponentName(templateType);
  const category = options.category || 'animations';
  
  const ModularComponent = ({ config, isPlaying, onComplete, ...props }) => {
    return renderFunction({ config, isPlaying, onComplete, ...props });
  };
  
  ModularComponent.displayName = `Modular_${componentName}`;
  
  // Auto-register
  const mediaType = modularAnimationSystem.getMediaTypeFromCategory(category);
  enhancedAnimationRegistry.register(
    templateType,
    ModularComponent,
    mediaType,
    {
      source: 'modular-factory',
      category,
      ...options
    }
  );
  
  return ModularComponent;
};

// Modular System Status Component
export const ModularSystemStatus = () => {
  const [status, setStatus] = React.useState(null);
  
  React.useEffect(() => {
    const updateStatus = () => {
      setStatus(modularAnimationSystem.getStatus());
    };
    
    updateStatus();
    const interval = setInterval(updateStatus, 5000);
    
    return () => clearInterval(interval);
  }, []);
  
  if (!status) return null;
  
  return (
    <div style={{
      position: 'fixed',
      bottom: '10px',
      right: '10px',
      background: '#1f2937',
      color: '#ffffff',
      padding: '16px',
      borderRadius: '8px',
      fontSize: '12px',
      maxWidth: '300px',
      zIndex: 9997,
      fontFamily: 'monospace'
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>🔧 Modular Animation System</div>
      <div>Discovered: {status.discoveredComponents}</div>
      <div>Loaded: {status.componentModules}</div>
      <div>Loading: {status.loadingPromises}</div>
      
      <details style={{ marginTop: '8px' }}>
        <summary style={{ cursor: 'pointer', fontSize: '11px' }}>Component Types</summary>
        <div style={{ fontSize: '10px', marginTop: '4px', maxHeight: '100px', overflow: 'auto' }}>
          {status.discoveredTypes.map(type => (
            <div key={type} style={{ marginLeft: '8px' }}>• {type}</div>
          ))}
        </div>
      </details>
    </div>
  );
};

export default modularAnimationSystem;